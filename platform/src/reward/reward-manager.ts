import Logger, { LogClass } from '../core/logging';
import { Inject, Singleton } from '../core/ioc';
import { Reward } from './reward';
import { NotFoundError, PagedResult, BigNumber } from '../core';
import { RewardStatus } from './reward-status';
import { RewardSummary } from './reward-summary';
import { NewReward } from './new-reward';
import { RewardFilter, RewardPendingFilter, RewardRecentFilter } from './reward-filter';
import { RewardUpdate } from './reward-update';
import { RewardRepository } from './repositories';
import { PlatformEventDispatcher } from '../core/events';
import { RewardAwardedEvent } from './events';
import { Websocket } from '../websocket';
import { RewardRequirementUpdate } from './reward-requirement-update';
import { RewardBalanceCache, RewardPendingCache, RewardSummaryCache } from './cache';
import _ from 'lodash';
import { RewardModelMapper } from './models/mappers';
import { RewardContributorUpdate } from './reward-contributor';
import { RewardTemplateSourceType } from './reward-template-source-type';
import { RewardUnclaimed } from './reward-unclaimed';
import { RewardClaimedTotals } from './reward-claimed-totals';

@Singleton
@LogClass()
export class RewardManager {
    constructor(
        @Inject private readonly balanceCache: RewardBalanceCache,
        @Inject private readonly repository: RewardRepository,
        @Inject private readonly eventDispatcher: PlatformEventDispatcher,
        @Inject private readonly websocket: Websocket,
        @Inject private readonly summaryCache: RewardSummaryCache,
        @Inject private readonly pendingCache: RewardPendingCache,
        @Inject private readonly modelMapper: RewardModelMapper) {
    }

    public async get(id: number): Promise<Reward | undefined> {
        return this.repository.get(id);
    }

    public async getRecent(filter: RewardRecentFilter): Promise<Reward[]> {
        return this.repository.getRecent(filter);
    }

    public async getAll(filter: RewardFilter): Promise<PagedResult<Reward>> {
        return this.repository.getAll(filter);
    }

    public async getPending(filter: RewardPendingFilter): Promise<Reward[]> {
        let rewards = await this.pendingCache.getAll(filter.userId);

        if (rewards.length === 0) {
            if (filter.cacheOnly)
                return [];

            rewards = await this.repository.getPending(filter.userId);
            await this.pendingCache.store(filter.userId, ...rewards);
        }

        if (filter.templateIdentifier)
            rewards = _.filter(rewards, r => r.template.identifier === filter.templateIdentifier);

        if (filter.currencyCode)
            rewards = _.filter(rewards, r => r.currencyCode === filter.currencyCode);

        if (filter.types && filter.types.length > 0)
            rewards = _.filter(rewards, r => filter.types?.includes(r.type)) as Reward[];

        if (filter.requirements && filter.requirements.length > 0)
            rewards = _.filter(rewards, r => _.some(r.requirements, e => filter.requirements?.includes(e.type)));

        if (filter.contributors && filter.contributors.length > 0)
            rewards = _.filter(rewards, r => _.some(r.contributors, e => filter.contributors?.includes(e.type)));

        if (filter.restrictions && filter.restrictions.length > 0)
            rewards = _.filter(rewards, r => _.some(r.restrictions, e => filter.restrictions?.includes(e)));

        return _.orderBy(rewards, r => r.createTime, 'asc');
    }

    public async getTotalClaimedForUser(userId: number): Promise<RewardClaimedTotals> {
        const cached = await this.balanceCache.getClaimedSummary(userId);

        if (cached)
            return cached;

        const summary = await this.repository.getTotalClaimedForUser(userId);
        await this.balanceCache.storeClaimedSummary(userId, summary);

        return summary;
    }

    public async getTotalClaimedForUserByBalanceIdAndSourceType(userId: number, balanceId: number, sourceType: RewardTemplateSourceType, sourceCurrency: string): Promise<BigNumber> {
        return this.repository.getTotalClaimedForUserByBalanceIdAndSourceType(userId, balanceId, sourceType, sourceCurrency);
    }

    public async getCountByTemplateId(id: number): Promise<number> {
        return this.repository.getCountByTemplateId(id);
    }

    public async add(reward: NewReward): Promise<Reward> {
        if (!reward.requirements?.length && !reward.contributors?.length && !(reward.expireTime && reward.awardOnExpiry))
            reward.status = RewardStatus.Processing;

        const added = await this.repository.add(reward);
        await this.summaryCache.increment(reward.userId, reward.templateId);

        if (added.status === RewardStatus.Pending)
            await this.pendingCache.store(added.user.id, added);

        await this.websocket.user(added.user.id)
            .type('Reward:Added')
            .payload(this.modelMapper.map(added))
            .send();

        return added;
    }

    public async getByBalanceId(balanceId: number): Promise<Reward[]> {
        return this.repository.getByBalanceId(balanceId);
    }

    public async update(id: number, update: RewardUpdate, notify: boolean = true): Promise<Reward> {
        const updated = await this.repository.update(id, update);
        await this.pendingCache.store(updated.user.id, updated);

        if (notify)
            await this.websocket.user(updated.user.id)
                .type('Reward:Updated')
                .payload(this.modelMapper.map(updated))
                .send();

        return updated;
    }

    public async updateRequirement(rewardId: number, update: RewardRequirementUpdate, notify: boolean = true): Promise<Reward> {
        const updated = await this.repository.updateRequirement(rewardId, update);
        await this.pendingCache.store(updated.user.id, updated);

        if (notify)
            await this.websocket.user(updated.user.id)
                .type('Reward:Updated')
                .payload(this.modelMapper.map(updated))
                .send();

        return updated;
    }

    public async updateTotalClaimedForUser(userId: number): Promise<RewardClaimedTotals> {
        const summary = await this.repository.getTotalClaimedForUser(userId);
        await this.balanceCache.storeClaimedSummary(userId, summary);
        return summary;
    }

    public async satisfyRequirements(rewardIdOrRef: number | Reward, notify: boolean = true): Promise<Reward> {
        const reward = typeof rewardIdOrRef === 'number'
            ? await this.get(rewardIdOrRef)
            : rewardIdOrRef;

        if (!reward)
            throw new NotFoundError(`Reward ${rewardIdOrRef} could not be found.`);

        if (reward.requirements.length === 0)
            return reward;

        const updated = await this.repository.satisfyRequirements(reward.id);

        await this.pendingCache.store(reward.user.id, updated);

        if (notify)
            await this.websocket.user(updated.user.id)
                .type('Reward:Updated')
                .payload(this.modelMapper.map(updated))
                .send();

        return updated;
    }

    public async updateContributor(rewardId: number, update: RewardContributorUpdate, notify: boolean = true): Promise<Reward> {
        const updated = await this.repository.updateContributor(rewardId, update);
        await this.pendingCache.store(updated.user.id, updated);

        if (notify)
            await this.websocket.user(updated.user.id)
                .type('Reward:Updated')
                .payload(this.modelMapper.map(updated))
                .send();

        return updated;
    }

    public async satisfyContributors(rewardIdOrRef: number | Reward, notify: boolean = true): Promise<Reward> {
        const reward = typeof rewardIdOrRef === 'number'
            ? await this.get(rewardIdOrRef)
            : rewardIdOrRef;

        if (!reward)
            throw new NotFoundError(`Reward ${rewardIdOrRef} could not be found.`);

        if (reward.contributors.length === 0)
            return reward;

        const updated = await this.repository.satisfyContributors(reward.id);

        await this.pendingCache.store(reward.user.id, updated);

        if (notify)
            await this.websocket.user(updated.user.id)
                .type('Reward:Updated')
                .payload(this.modelMapper.map(updated))
                .send();

        return updated;
    }

    public async setStatus(id: number, newStatus: RewardStatus): Promise<Reward> {
        const reward = await this.get(id);

        if (!reward)
            throw new NotFoundError(`Reward ${id} could not be found.`);

        if (reward.status === newStatus)
            return reward;

        const updated = await this.repository.setStatus(id, newStatus);

        if ([RewardStatus.Cancelled, RewardStatus.Failed].includes(newStatus))
            await this.summaryCache.decrement(reward.user.id, reward.template.id);

        await Promise.all([
            this.sendAwardedEvents(updated),
            this.websocket.user(reward.user.id)
                .type('Reward:Updated')
                .payload(this.modelMapper.map(updated))
                .send()
        ]);

        switch (updated.status) {
            case RewardStatus.Pending:
                await this.pendingCache.store(reward.user.id, reward);
                break;

            default:
                await this.pendingCache.remove(reward.user.id, reward.id);
        }

        return updated;
    }

    public async getAllSummariesForUser(userId: number): Promise<RewardSummary[]> {
        const cached = await this.summaryCache.getAll(userId);

        if (cached.length > 0)
            return cached;

        const summaries = await this.repository.getAllSummariesForUser(userId);
        await this.summaryCache.store(userId, ...summaries);
        return summaries;
    }

    public async getSummaryForUser(userId: number, rewardTemplateId: number): Promise<RewardSummary | undefined> {
        const cached = await this.summaryCache.get(userId, rewardTemplateId);

        if (cached)
            return cached;

        const summary = await this.repository.getSummaryForUser(userId, rewardTemplateId);
        await this.summaryCache.store(userId, summary);
        return summary;
    }

    public async refreshUserRewardSummaries(userId: number): Promise<void> {
        await this.summaryCache.remove(userId);
        await this.getAllSummariesForUser(userId);
    }

    public async getByInventoryItemId(inventoryItemId: number): Promise<Reward | undefined> {
        return this.repository.getByInventoryItemId(inventoryItemId);
    }

    public async getUnclaimedRewards(userId: number, sourceType: RewardTemplateSourceType[]): Promise<RewardUnclaimed[]> {
        return this.repository.getUnclaimedRewards(userId, sourceType);
    }

    public async getRewardSummariesForUser(userId: number, dateFrom: Date, dateTo: Date): Promise<{ [key: string]: string; }[]> {
        return this.repository.getRewardSummariesForUser(userId, dateFrom, dateTo);
    }

    private async sendAwardedEvents(reward: Reward): Promise<void> {
        if (reward.status !== RewardStatus.Completed)
            return;

        try {
            await this.eventDispatcher.send(new RewardAwardedEvent(reward));
        } catch (err) {
            Logger.warn(`Could not send awarded events for reward ${reward.id}`, { error: err });
        }
    }
}