import { Inject, Singleton } from '../../../core/ioc';
import { JsonSerialiser, Redis } from '../../../core';
import { CacheKeyGenerator } from '../../../core/cache';
import { LogClass } from '../../../core/logging';
import { RewardBalanceBatchJob } from './reward-balance-job';
import { RewardBalanceType } from '../../reward-balance-type';
import { RewardBalanceJobStatus } from './reward-balance-job-status';
import { RewardBalanceBatchJobItem } from './reward-balance-job-item';
import { RewardBalance } from '../../reward-balance';
import moment from 'moment';
import _ from 'lodash';

@Singleton
@LogClass()
export class RewardBalanceJobCache {
    private readonly WINBACK_EXPIRY_DAYS = 1;
    private readonly DAILY_EXPIRY_DAYS = 6;
    private readonly DAILY_ALLOCATION_EXPIRY_DAYS = 1;
    private readonly WEEKLY_EXPIRY_DAYS = 6;
    private readonly MONTHLY_EXPIRY_DAYS = 20;
    private readonly cacheKeyGenerator = new CacheKeyGenerator('REWARD:Balance:Batch');

    constructor(
        @Inject private readonly redis: Redis,
        @Inject private readonly jsonSerialiser: JsonSerialiser) {
    }

    public async get(jobId: string, type: RewardBalanceType): Promise<RewardBalanceBatchJob | undefined> {
        const cacheKey = this.cacheKeyGenerator.generate(`${type}:Job`);
        const rawData = await this.redis.cluster.hget(cacheKey, jobId);

        if (!rawData)
            return undefined;

        return this.jsonSerialiser.deserialise<RewardBalanceBatchJob>(rawData);
    }

    public async getItems(jobId: string, type: RewardBalanceType, status: RewardBalanceJobStatus): Promise<RewardBalanceBatchJobItem[]> {
        const cacheKey = this.cacheKeyGenerator.generate(`${type}:${jobId}:${status}`);

        const rawJobs = await this.redis.cluster.hgetall(cacheKey);

        if (!rawJobs)
            return [];

        return Object.values(rawJobs).map(j => this.jsonSerialiser.deserialise<RewardBalanceBatchJobItem>(j));
    }

    public async getNextBatch(jobId: string, type: RewardBalanceType, count: number, status: RewardBalanceJobStatus): Promise<RewardBalance[]> {
        const cacheKey = this.cacheKeyGenerator.generate(`${type}:${jobId}:${status}`);
        const rawData = await this.redis.cluster.rpop(cacheKey, count);

        if (!rawData || !rawData.length)
            return [];

        return _.map(rawData, r => this.jsonSerialiser.deserialise<RewardBalance>(r));
    }

    public async add(job: RewardBalanceBatchJob): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(`${job.type}:Job`);
        await this.redis.cluster.hset(cacheKey, job.id, this.jsonSerialiser.serialise(job));
        await this.addIndex(job);
    }

    public async addItems(job: RewardBalanceBatchJob, status: RewardBalanceJobStatus, rewardBalance: RewardBalanceBatchJobItem[]): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(`${job.type}:${job.id}:${status}`);
        await this.redis.cluster
            .pipeline()
            .lpush(cacheKey, ...rewardBalance.map((r) => this.jsonSerialiser.serialise(r)))
            .expireat(cacheKey, this.itemsExpireAt(job.type))
            .exec();
    }

    public async addItem(balanceJobItem: RewardBalanceBatchJobItem, rewardBalanceJobStatus: RewardBalanceJobStatus): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(`${balanceJobItem.balance.type}:${balanceJobItem.jobId}:${rewardBalanceJobStatus}`);
        await this.redis.cluster
            .pipeline()
            .lpush(cacheKey, this.jsonSerialiser.serialise(balanceJobItem))
            .exec();
    }

    public async update(job: RewardBalanceBatchJob): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(`${job.type}:Job`);
        await this.redis.cluster.hset(cacheKey, job.id, this.jsonSerialiser.serialise(job));
    }

    public async removeItems(jobId: string, type: RewardBalanceType, ...statuses: RewardBalanceJobStatus[]): Promise<void> {
        const cacheKeys = statuses.map(status => this.cacheKeyGenerator.generate(`${type}:${jobId}:${status}`));

        for (const cacheKey of cacheKeys)
            await this.redis.cluster.del(cacheKey);
    }

    public async removeJobs(type: RewardBalanceType): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(`${type}:Job:Index`);
        const maxRange = moment().subtract(90, 'days').valueOf().toString();
        const ids = await this.redis.cluster.zrangebyscore(cacheKey, '-inf', maxRange);

        if (!ids.length)
            return;

        const pipeline = this.redis.cluster.pipeline();

        for (const id of ids) {
            const job = await this.get(id, type);
            if (job && job.status === RewardBalanceJobStatus.Complete) {
                pipeline.zrem(cacheKey, job.id);
                pipeline.hdel(this.cacheKeyGenerator.generate(`${type}:Job`), job.id);
            }
        }

        await pipeline.exec();
    }

    public async lock<T>(type: RewardBalanceType, handler: () => Promise<T>): Promise<T> {
        const cacheKey = this.cacheKeyGenerator.generate(`${type}:Job`);
        return this.redis.lock(cacheKey, handler, 30000);
    }

    private async addIndex(job: RewardBalanceBatchJob): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate(`${job.type}:Job:Index`);
        await this.redis.cluster.zadd(cacheKey, 'NX', job.startTime.toString(), job.id.toString());
    }

    private itemsExpireAt(balanceType: RewardBalanceType): number {
        switch (balanceType) {
            case RewardBalanceType.WinBack:
                return moment()
                    .add(this.WINBACK_EXPIRY_DAYS, 'days')
                    .unix();

            case RewardBalanceType.Daily:
                return moment()
                    .add(this.DAILY_EXPIRY_DAYS, 'days')
                    .unix();

            case RewardBalanceType.DailyAllocation:
                return moment()
                    .add(this.DAILY_ALLOCATION_EXPIRY_DAYS, 'days')
                    .unix();

            case RewardBalanceType.Weekly:
                return moment()
                    .add(this.WEEKLY_EXPIRY_DAYS, 'days')
                    .unix();

            case RewardBalanceType.Monthly:
                return moment()
                    .add(this.MONTHLY_EXPIRY_DAYS, 'days')
                    .unix();

            default:
                return moment()
                    .add(1, 'days')
                    .unix();
        }
    }
}