import { Inject, Singleton } from '../../../core/ioc';
import Logger, { LogClass } from '../../../core/logging';
import { Config, DEFAULT_REGION, JsonSerialiser, KeyGenerator, NotFoundError } from '../../../core';
import { RewardBalance } from '../../reward-balance';
import { RewardBalanceType } from '../../reward-balance-type';
import { RewardBalanceJobStatus } from './reward-balance-job-status';
import { RewardBalanceJobCache } from './reward-balance-job.cache';
import { RewardBalanceBatchJob } from './reward-balance-job';
import { RewardBalanceBatchJobItem } from './reward-balance-job-item';
import AWS, { AWSError } from 'aws-sdk';

@Singleton
@LogClass()
export class RewardBalanceJobManager {
    constructor(
        @Inject private readonly keyGenerator: KeyGenerator,
        @Inject private readonly jsonSerialiser: <PERSON><PERSON><PERSON>erial<PERSON>,
        @Inject private readonly cache: RewardBalanceJobCache) {
    }

    public async get(id: string, type: RewardBalanceType): Promise<RewardBalanceBatchJob | undefined> {
        return this.cache.get(id, type);
    }

    public async create(type: RewardBalanceType, balances: RewardBalance[]): Promise<RewardBalanceBatchJob> {
        const job: RewardBalanceBatchJob = {
            id: this.keyGenerator.generate(),
            type,
            status: RewardBalanceJobStatus.Pending,
            pending: balances.length,
            complete: 0,
            failed: 0,
            batchSize: 100,
            total: balances.length,
            progress: 0,
            restartCount: 0,
            startTime: Date.now(),
            endTime: 0
        };

        const items: RewardBalanceBatchJobItem[] = balances.map(balance => ({
            jobId: job.id,
            balance
        }));

        await this.cache.add(job);
        await this.cache.addItems(job, RewardBalanceJobStatus.Pending, items);
        await this.start(job.id, type);
        return job;
    }

    public async getNextBatch(jobId: string, type: RewardBalanceType): Promise<RewardBalance[]> {
        const job = await this.get(jobId, type);

        if (!job)
            throw new NotFoundError(`Reward balance batch job ${jobId} not found.`);

        if (job.status === RewardBalanceJobStatus.Pending)
            await this.cache.update({ ...job, status: RewardBalanceJobStatus.Running });

        return this.cache.getNextBatch(jobId, type, job.batchSize, RewardBalanceJobStatus.Pending);
    }


    public async getItems(jobId: string, type: RewardBalanceType, status: RewardBalanceJobStatus): Promise<RewardBalanceBatchJobItem[]> {
        return this.cache.getItems(jobId, type, status);
    }

    public async start(jobId: string, type: RewardBalanceType): Promise<void> {
        const job = await this.get(jobId, type);

        if (!job)
            throw new NotFoundError(`Reward balance job ${jobId} not found.`);

        const stepFunctions = new AWS.StepFunctions({
            region: DEFAULT_REGION
        });

        const startArgs: AWS.StepFunctions.StartExecutionInput = {
            stateMachineArn: `arn:aws:states:${DEFAULT_REGION}:${Config.accountId}:stateMachine:processReward`,
            input: this.jsonSerialiser.serialise({ id: job.id, type, batchSize: job.batchSize })
        };

        try {
            const response = await stepFunctions.startExecution(startArgs).promise();
            await this.cache.update({ ...job, status: RewardBalanceJobStatus.Running, executionArn: response.executionArn });
        } catch (err) {
            Logger.error(`Reward balance batch job ${job.id} failed to start execution.`, err);
        }
    }

    public async setItemComplete(item: RewardBalanceBatchJobItem): Promise<RewardBalanceBatchJob> {
        return this.cache.lock(item.balance.type, async () => {
            const job = await this.get(item.jobId, item.balance.type);

            if (!job)
                throw new NotFoundError(`Reward balance job item ${item.jobId} not found.`);

            job.pending--;
            job.complete++;
            job.progress = Math.round(((job.total - job.pending) / job.total) * 100);

            await this.cache.update(job);
            await this.cache.addItem({ ...item, processTime: Date.now() }, RewardBalanceJobStatus.Complete);
            await this.checkPoint(job);
            return job;
        });
    }

    public async setItemFailed(item: RewardBalanceBatchJobItem, reason: string): Promise<RewardBalanceBatchJob> {
        return this.cache.lock(item.balance.type, async () => {
            const job = await this.get(item.jobId, item.balance.type);

            if (!job)
                throw new NotFoundError(`Reward balance job item ${item.jobId} not found.`);

            job.pending--;
            job.failed++;
            job.progress = Math.round(((job.total - job.pending) / job.total) * 100);

            await this.cache.update(job);
            await this.cache.addItem({ ...item, reason }, RewardBalanceJobStatus.Failed);
            await this.checkPoint(job);
            return job;
        });
    }

    // TODO - maybe do this with a cron job
    public async clearJobs(type: RewardBalanceType): Promise<void> {
        await this.cache.removeJobs(type);
    }

    private async checkPoint(job: RewardBalanceBatchJob): Promise<void> {
        const processed = job.complete + job.failed;

        if (processed > 0 && processed % 1000 === 0 && job.pending > 0) {
            await this.restart(job.id, job.type);
            return;
        }

        if (job.pending > 0)
            return;

        job.status = job.failed > 0
            ? RewardBalanceJobStatus.Failed
            : RewardBalanceJobStatus.Complete;

        await this.cache.update(job);
        await this.completed(job.id, job.type);

        const CLEAR_STATUS = [
            RewardBalanceJobStatus.Complete
        ];

        await this.cache.removeItems(job.id, job.type, ...CLEAR_STATUS);
    }

    private async restart(jobId: string, type: RewardBalanceType): Promise<void> {
        const restartJob = await this.get(jobId, type);

        if (!restartJob)
            throw new NotFoundError(`Reward balance job item ${jobId} not found.`);

        restartJob.restartCount++;

        await this.stop(jobId, type);
        await this.cache.update(restartJob);
        await this.cache.removeItems(jobId, type, RewardBalanceJobStatus.Complete);
        await this.start(jobId, type);
    }

    private async stop(jobId: string, type: RewardBalanceType): Promise<void> {
        const job = await this.get(jobId, type);

        if (!job) {
            Logger.warn(`Reward balance batch job ${jobId} not found.`);
            return;
        }

        await this.completed(jobId, type);
        await this.terminate(jobId, type);
    }

    private async completed(jobId: string, type: RewardBalanceType): Promise<void> {
        const job = await this.get(jobId, type);

        if (!job) {
            Logger.warn(`Reward balance batch job ${jobId} not found.`);
            return;
        }

        await this.cache.update({ ...job, endTime: Date.now() });
    }

    private async terminate(jobId: string, type: RewardBalanceType): Promise<void> {
        const job = await this.get(jobId, type);

        if (!job) {
            Logger.warn(`Reward balance batch job ${jobId} not found.`);
            return;
        }

        if (!job.executionArn) {
            Logger.warn(`Reward balance batch job ${jobId} does not have execution ARN.`);
            return;
        }

        const stepFunctions = new AWS.StepFunctions({
            region: DEFAULT_REGION
        });

        const stopArgs: AWS.StepFunctions.StopExecutionInput = {
            executionArn: job.executionArn,
            cause: 'Cancellation'
        };

        try {
            await stepFunctions.stopExecution(stopArgs).promise();
        } catch (err) {
            const error = err as AWSError;

            if (error.code !== 'ExecutionDoesNotExist')
                Logger.error(`Reward Balance batch job ${jobId} failed to stop execution.`, err);
        }

        if (job.executionArn)
            delete job.executionArn;

        await this.cache.update(job);
    }
}
