import { <PERSON>ton, Inject } from '../../../core/ioc';
import { LogClass, LogLevel } from '../../../core/logging';
import { User, UserBonusStatus } from '../../../user';
import { RewardBalance, RewardBalanceManager, RewardBalanceStatus, RewardBalanceType } from '../..';

@Singleton
@LogClass({ level: LogLevel.Info })
export class RewardBalanceUserBonusStatus {
    constructor(
        @Inject private readonly manager: RewardBalanceManager) {
    }

    public async validate(user: User, entry: RewardBalance): Promise<boolean> {
        if (user.bonusStatus === UserBonusStatus.Active)
            return true;

        if (entry.type === RewardBalanceType.DailyAllocation) {
            const allocationCount = await this.manager.updateDailyCount(entry.id);

            if (allocationCount && allocationCount.count === 0) {
                await this.manager.setStatus(RewardBalanceStatus.Complete, entry.id);
                await this.manager.remove(entry.userId, entry.type);
            }

            return false;
        }

        await this.manager.setClosed(entry.id, entry.userId, entry.type);

        return false;
    }
}