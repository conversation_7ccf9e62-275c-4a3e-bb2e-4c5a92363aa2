import { <PERSON>ton, Inject } from '../../../core/ioc';
import Logger, { LogClass, LogLevel } from '../../../core/logging';
import { RewardBalance, RewardBalanceManager, RewardBalanceStatus, RewardBalanceType } from '../../../reward';
import { RewardBalanceValidationResult } from './reward-balance-validation-result';

@Singleton
@LogClass({ level: LogLevel.Info })
export class RewardBalanceValidateCacheEntry {
    constructor(
        @Inject private readonly manager: RewardBalanceManager) {
    }

    public async validate(entry: RewardBalance): Promise<RewardBalanceValidationResult> {
        // const cachedBalance = await this.manager.getByUserId(entry.userId, entry.type);

        // if (!cachedBalance) {
        //     Logger.warn(`Cached ${entry.type} with id ${entry.id} for user ${entry.userId} not found. Closing...`);
        //     await this.manager.setStatus(RewardBalanceStatus.Closed, entry.id);
        //     return false;
        // }

        // if (cachedBalance.id !== entry.id) {
        //     Logger.warn(`${entry.type} entry with id ${entry.id} for user ${entry.userId} does not match cached balance ${cachedBalance.id}. Closing...`);
        //     await this.manager.setStatus(RewardBalanceStatus.Closed, entry.id);
        //     return false;
        // }

        // if (cachedBalance.type === RewardBalanceType.DailyAllocation && cachedBalance.count <= 0) {
        //     Logger.warn(`Cached Daily allocation with id ${entry.id} for user ${entry.userId} has no remaining count. Completing...`);
        //     await this.manager.setStatus(RewardBalanceStatus.Complete, entry.id);
        //     return false;
        // }

        // return true;

        return this.validateCacheEntry(entry);
    }

    private async validateCacheEntry(entry: RewardBalance): Promise<RewardBalanceValidationResult> {
        const cachedBalance = await this.manager.getByUserId(entry.userId, entry.type);

        if (!cachedBalance)
            return {
                valid: false,
                status: RewardBalanceStatus.Closed,
                message: `Cached ${entry.type} with id ${entry.id} for user ${entry.userId} not found. Closing...`
            };

        if (cachedBalance.id !== entry.id)
            return {
                valid: false,
                status: RewardBalanceStatus.Closed,
                message: `${entry.type} entry with id ${entry.id} for user ${entry.userId} does not match cached balance ${cachedBalance.id}. Closing...`
            };

        if (cachedBalance.type === RewardBalanceType.DailyAllocation && cachedBalance.count <= 0)
            return {
                valid: false,
                status: RewardBalanceStatus.Complete,
                message: `Cached Daily allocation with id ${entry.id} for user ${entry.userId} has no remaining count. Completing...`
            };

        return {
            valid: true
        };
    }
}