import { <PERSON>ton, Inject } from '../../../core/ioc';
import Logger, { LogClass, LogLevel } from '../../../core/logging';
import { RewardBalance, RewardBalanceManager, RewardBalanceStatus, RewardBalanceType, RewardManager } from '../..';

@Singleton
@LogClass({ level: LogLevel.Info })
export class RewardBalanceValidateEntryStatus {
    constructor(
        @Inject private readonly manager: RewardBalanceManager,
        @Inject private readonly rewardManager: RewardManager) {
    }

    public async validate(entry: RewardBalance): Promise<boolean> {
        const balance = await this.manager.get(entry.id);

        if (!balance) {
            Logger.warn(`Balance ${entry.type} with id ${entry.id} for user ${entry.userId} not found. Skipping...`);
            return false;
        }

        if ([RewardBalanceStatus.Complete, RewardBalanceStatus.Closed].includes(balance.status)) {
            Logger.warn(`Balance ${entry.type} with id ${entry.id} for user ${entry.userId} is already ${balance.status}. Skipping...`);
            return false;
        }

        if (balance.type !== RewardBalanceType.DailyAllocation && balance.status === RewardBalanceStatus.Processing) {
            Logger.warn(`Balance ${entry.type} with id ${entry.id} for user ${entry.userId} is in a processing state.`);

            const existingReward = await this.rewardManager.getByBalanceId(entry.id);

            if (existingReward) {
                Logger.warn(`Reward ${entry.type} with balance ${entry.id} for user ${entry.userId} already exists. Setting status to Complete...`);
                await this.manager.setStatus(RewardBalanceStatus.Complete, entry.id);
                return false;
            }

            Logger.warn(`Reward ${entry.type} with balance ${entry.id} for user ${entry.userId} does not exist. Setting status to InProgress...`);
            await this.manager.setStatus(RewardBalanceStatus.InProgress, entry.id);

            return true;
        }

        return true;
    }
}