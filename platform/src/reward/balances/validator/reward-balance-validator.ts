import { <PERSON>ton, Inject, IocContainer } from '../../../core/ioc';
import { LogClass, LogLevel } from '../../../core/logging';
import { User } from '../../../user';
import { RewardBalance } from '../../../reward';
import { RewardBalanceValidateCacheEntry } from './reward-balance-validate-cache-entry';
import { RewardBalanceValidateReward } from './reward-balance-validate-reward';
import { RewardBalanceValidateUserBonusStatus } from './reward-balance-validate-user-bonus-status';

@Singleton
@LogClass({ level: LogLevel.Info })
export class RewardBalanceValidator {
    constructor(
        @Inject private readonly validateCacheEntry: RewardBalanceValidateCacheEntry,
        @Inject private readonly validateReward: RewardBalanceValidateReward,
        @Inject private readonly validateUserBonusStatus: RewardBalanceValidateUserBonusStatus) {
    }

    public async validate(user: User, entry: RewardBalance): Promise<boolean> {

        const balanceValidators = [
            RewardBalanceValidateUserBonusStatus,
            RewardBalanceValidateCacheEntry,
            RewardBalanceValidateReward
        ];

        const validators = balanceValidators.map(v => IocContainer.get(v));

        for (const validator of validators) {
            const result = await Promise.all(validators.map(v => IocContainer.get(v)));
        }


        // const validateUserBonusStatus = await this.validateUserBonusStatus.validate(user, entry);
        // const validateCacheEntry = await this.validateCacheEntry.validate(entry);
        // const validateEntryStatus = await this.validateReward.validate(entry);

        // if (!validateCacheEntry || !validateEntryStatus || !validateUserBonusStatus)
        //     return false;

        // return true;
    }
}