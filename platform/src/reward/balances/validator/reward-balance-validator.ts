import { Singleton, Inject } from '../../../core/ioc';
import { LogClass, LogLevel } from '../../../core/logging';
import { User } from '../../../user';
import { RewardBalance } from '../../../reward';
import { RewardBalanceValidateCacheEntry } from './reward-balance-validate-cache-entry';
import { RewardBalanceValidateEntryStatus } from './reward-balance-validate-entry-status';
import { RewardBalanceUserBonusStatus } from './reward-balance-user-bonus-status';

@Singleton
@LogClass({ level: LogLevel.Info })
export class RewardBalanceValidator {
    constructor(
        @Inject private readonly validateCacheEntry: RewardBalanceValidateCacheEntry,
        @Inject private readonly validateEntryStatus: RewardBalanceValidateEntryStatus,
        @Inject private readonly validateUserBonusStatus: RewardBalanceUserBonusStatus) {
    }

    public async validate(user: User, entry: RewardBalance): Promise<boolean> {
        const validateUserBonusStatus = await this.validateUserBonusStatus.validate(user, entry);
        const validateCacheEntry = await this.validateCacheEntry.validate(entry);
        const validateEntryStatus = await this.validateEntryStatus.validate(entry);

        if (!validateCacheEntry || !validateEntryStatus || !validateUserBonusStatus)
            return false;

        return true;
    }
}