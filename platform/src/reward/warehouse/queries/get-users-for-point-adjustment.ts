export const GET_USERS_FOR_POINT_ADJUSTMENT = `
    WITH GameMultipliers AS (
        SELECT
            g.id AS gameId,
            COALESCE(CAST(PARSE_JSON(g.metadata):rewardWagerMultiplier AS DECIMAL(10,2)), 1) AS rewardWagerMultiplier
        FROM
            fivetran_cdc_platform.game g
    ),
    GameWagering AS (
        SELECT
            gpas.userId,
            gpas.gameId,
            gpas.currencyCode,
            COALESCE(SUM(gpas.buyInEffectiveAmount), 0) AS buyInEffectiveAmount,
            COALESCE(ROUND(SUM((gpas.buyInEffectiveAmount * cr.exchangeRate)), c.decimals), 0) AS buyInEffectiveAmountBase
        FROM
            fivetran_cdc_platform.game_play_action_statistics gpas
        INNER JOIN
            fivetran_cdc_platform.currency_rate cr ON cr.target = :baseCurrency AND cr.source = gpas.currencyCode AND cr.date = gpas.date
        INNER JOIN
            fivetran_cdc_platform.currency c ON c.code = cr.target
        INNER JOIN
            fivetran_cdc_platform.game g ON g.id = gpas.gameId
        WHERE
            gpas.date BETWEEN DATE(:dateFrom) AND DATE(:dateTo)
        GROUP BY
            gpas.userId,
            gpas.gameId,
            gpas.currencyCode,
            c.decimals
    ),
    AdjustedGameWagering AS (
        SELECT
            gw.userId,
            SUM(gw.buyInEffectiveAmountBase) AS buyInEffectiveAmountBase,
            COALESCE(SUM(gw.buyInEffectiveAmountBase * COALESCE(gm.rewardWagerMultiplier, 0)), 0) AS adjustedBuyInEffectiveAmountBase
        FROM
            GameWagering gw
        LEFT JOIN
            GameMultipliers gm ON gw.gameId = gm.gameId
        GROUP BY
            gw.userId
    ),
    LevelChanged AS (
        SELECT DISTINCT
            rpuh.userId
        FROM
            fivetran_cdc_platform.reward_points_user_history rpuh
        WHERE
            rpuh.type = 'StreakX'
        AND
            rpuh.createTime BETWEEN :dateFrom AND :dateTo
        AND
            rpuh.operationType = 'LevelChange'
        AND
           rpuh.currentLevel > rpuh.previousLevel
    )
    SELECT
        :dateTo AS "date",
        rpu.type AS "type",
        rpu.userId AS "userId",
        u.displayName AS "displayName",
        rpu.points AS "points",
        rpu.streak AS "streak",
        rpu.level AS "level",
        CASE WHEN lc.userId IS NOT NULL THEN 1 ELSE 0 END AS "levelChanged",
        COALESCE(agw.buyInEffectiveAmountBase, 0) AS "buyInEffectiveAmountBase",
        COALESCE(agw.adjustedBuyInEffectiveAmountBase, 0) AS "adjustedBuyInEffectiveAmountBase",
        rpu.lastCycleTime AS "lastCycleTime",
        rpu.lastProcessedTime AS "lastProcessedTime"
    FROM
        fivetran_cdc_platform.reward_points_user rpu
    LEFT JOIN
        AdjustedGameWagering agw ON agw.userid = rpu.userId
    LEFT JOIN
        LevelChanged lc ON lc.userId = rpu.userId
    INNER JOIN
        fivetran_cdc_platform.user u ON u.id = rpu.userId
    WHERE
        (agw.adjustedBuyInEffectiveAmountBase > 0 OR (rpu.points > 0 OR rpu.streak > 0))
    AND
        rpu.enabled = 1
    AND
        (rpu.lastCycleTime IS NULL OR DATE(rpu.lastCycleTime) < DATE(:dateTo))
    AND
        (:userIds IS NULL OR :userIds = '' OR rpu.userId IN (SELECT VALUE FROM TABLE(SPLIT_TO_TABLE(:userIds, ','))))
    ORDER BY
        agw.adjustedBuyInEffectiveAmountBase DESC
`;