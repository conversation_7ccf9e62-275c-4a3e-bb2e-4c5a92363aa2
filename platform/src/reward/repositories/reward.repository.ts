import { DBRepository } from '../../core/db';
import Logger, { LogClass } from '../../core/logging';
import { Inject, Singleton } from '../../core/ioc';
import { RewardContributorEntityMapper, RewardEntityMapper, RewardRequirementEntityMapper } from '../entities/mappers';
import { InventoryFundsRewardEntity, RewardContributorEntity, RewardEntity, RewardRequirementEntity, RewardTemplateEntity } from '../entities';
import { Reward } from '../reward';
import { RewardStatus } from '../reward-status';
import moment from 'moment';
import { NewReward } from '../new-reward';
import { RewardUpdate } from '../reward-update';
import { RewardFilter, RewardRecentFilter } from '../reward-filter';
import { BigNumber, PagedResult } from '../../core';
import { NumericTransformer, convertOrdering, BigNumberTransformer } from '../../core/db/orm';
import { RewardSummary } from '../reward-summary';
import { UserEntity } from '../../user/entities';
import { RewardRequirementUpdate } from '../reward-requirement-update';
import { FindManyOptions } from '../../core/db/orm';
import { RewardContributorUpdate } from '../reward-contributor';
import { RewardTemplateSourceType } from '../reward-template-source-type';
import { RewardUnclaimed } from '../reward-unclaimed';
import { RewardClaimedTotals } from '../reward-claimed-totals';

@Singleton
@LogClass()
export class RewardRepository extends DBRepository {
    constructor(
        @Inject private readonly rewardMapper: RewardEntityMapper,
        @Inject private readonly requirementMapper: RewardRequirementEntityMapper,
        @Inject private readonly contributorMapper: RewardContributorEntityMapper) {
        super();
    }

    public async get(id: number): Promise<Reward | undefined> {
        const manager = await this.getManager();
        const entity = await manager.findOne(RewardEntity, {
            where: { id },
            relations: [
                'user',
                'template',
                'requirements',
                'contributors'
            ]
        });

        if (!entity)
            return undefined;

        return this.rewardMapper.fromEntity(entity);
    }

    public async getRecent(filter: RewardRecentFilter): Promise<Reward[]> {
        if (filter.limit === 0)
            return [];

        const manager = await this.getManager();
        const query = manager
            .createQueryBuilder(RewardEntity, 'reward')
            .innerJoinAndMapOne('reward.template', RewardTemplateEntity, 'template', 'reward.templateId = template.id')
            .innerJoinAndMapOne('reward.user', UserEntity, 'user', 'reward.userId = user.id')
            .leftJoinAndMapMany('reward.requirements', RewardRequirementEntity, 'requirement', 'requirement.rewardId = reward.id')
            .where('reward.userId = :userId', { userId: filter.userId });

        if (filter.templateIdentifier)
            query.andWhere(`template.identifier = :templateIdentifier `, { templateIdentifier: filter.templateIdentifier });

        if (filter.status && filter.status.length > 0)
            query.andWhere('reward.status IN (:statuses)', { statuses: filter.status });

        query.take(Math.min(filter.limit ?? 10, 10));

        query.orderBy('reward.id', 'DESC');

        const entities = await query.getMany();

        return entities.map(a => this.rewardMapper.fromEntity(a));
    }

    public async getByInventoryItemId(inventoryItemId: number): Promise<Reward | undefined> {
        const manager = await this.getManager();
        const entity = await manager.findOne(InventoryFundsRewardEntity, {
            where: { inventoryItemId },
            relations: [
                'user',
                'template',
                'requirements'
            ]
        });

        if (!entity)
            return undefined;

        return this.rewardMapper.fromEntity(entity);
    }

    public async getUnclaimedRewards(userId: number, sourceType: RewardTemplateSourceType[]): Promise<RewardUnclaimed[]> {
        const manager = await this.getManager();
        const entity = await manager.createQueryBuilder(RewardEntity, 'reward')
            .leftJoinAndMapOne('reward.inventoryItem', 'reward.inventoryItem', 'inventoryItem')
            .select([
                'ROUND(reward.sourceAmount, 2) AS sourceAmount',
                'reward.sourceType AS sourceType',
                'reward.claimFromTime AS claimFromTime',
                'reward.expireTime AS expireTime',
                'reward.status AS status',
                'inventoryItem.id AS inventoryItemId',
                'inventoryItem.batchId AS inventoryItemBatchId'
            ])
            .where('reward.userId = :userId', { userId })
            .andWhere('reward.status = :status', { status: RewardStatus.Completed })
            .andWhere('reward.sourceType IN (:...sourceType)', { sourceType })
            .andWhere('reward.expireTime > CURRENT_TIMESTAMP')
            .andWhere('inventoryItem.batchId IS NULL')
            .andWhere('inventoryItem.enabled = 1')
            .getRawMany();

        return entity;
    }

    public async getTotalClaimedForUser(userId: number): Promise<RewardClaimedTotals> {
        const manager = await this.getManager();
        const entity = await manager.createQueryBuilder(RewardEntity, 'reward')
            .select([
                `ROUND(SUM(CASE WHEN sourceType = 'WinBack' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS winBack`,
                `ROUND(SUM(CASE WHEN sourceType = 'DailyReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2)AS dailyReward`,
                `ROUND(SUM(CASE WHEN sourceType = 'WeeklyReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS weeklyReward`,
                `ROUND(SUM(CASE WHEN sourceType = 'MonthlyReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS monthlyReward`,
                `ROUND(SUM(CASE WHEN sourceType = 'RankUpReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS rankUpReward`,
                `ROUND(SUM(CASE WHEN sourceType IN('WinBack', 'DailyReward', 'WeeklyReward', 'MonthlyReward', 'RankUpReward' ) AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS total`
            ])
            .where('reward.userId = :userId', { userId })
            .getRawOne();

        return entity;
    }

    public async getTotalClaimedForUserByBalanceIdAndSourceType(userId: number, balanceId: number, sourceType: RewardTemplateSourceType, sourceCurrency: string): Promise<BigNumber> {
        const manager = await this.getManager();
        const entity = await manager.createQueryBuilder(RewardEntity, 'reward')
            .select([
                `COALESCE(ROUND(SUM(sourceAmount), 2), 0) AS total`,
            ])
            .where('reward.userId = :userId', { userId })
            .andWhere('reward.balanceId = :balanceId', { balanceId })
            .andWhere('reward.sourceType = :sourceType', { sourceType })
            .andWhere('reward.sourceCurrencyCode = :sourceCurrency', { sourceCurrency })
            .andWhere('reward.status = :status', { status: RewardStatus.Claimed })
            .getRawOne();

        return new BigNumber(entity.total);
    }

    public async setStatus(id: number, status: RewardStatus): Promise<Reward> {
        const manager = await this.getManager();
        const update: Partial<RewardEntity> = {
            status
        };

        if (status === RewardStatus.Completed)
            update.awardedTime = moment.utc().toDate();

        await manager.update(RewardEntity, id, update);
        return await this.primary(r => r.get(id)) as Reward;
    }

    public async add(reward: NewReward): Promise<Reward> {
        const entity = this.rewardMapper.newToEntity(reward);
        const id = await this.transaction(async manager => {
            const created = await manager.save(entity);
            const updated = this.rewardMapper.updateFromNewEntity(created.id, entity, reward);
            await manager.save(updated);
            return created.id;
        });

        return await this.primary(r => r.get(id)) as Reward;
    }

    public async update(id: number, update: RewardUpdate): Promise<Reward> {
        const entity = this.rewardMapper.updateToEntity(id, update);
        Logger.info('Updating reward', { id, update, entity });
        const manager = await this.getManager();
        await manager.save(entity, { reload: false });
        return await this.primary(r => r.get(id)) as Reward;
    }

    public async updateRequirement(rewardId: number, update: RewardRequirementUpdate): Promise<Reward> {
        const entity = this.requirementMapper.updateToEntity(update);

        await this.transaction(async manager => {
            await manager.update(RewardRequirementEntity, update.id, entity);
            await manager.update(RewardEntity, rewardId, {
                updateTime: () => 'CURRENT_TIMESTAMP'
            });
        });

        return await this.primary(r => r.get(rewardId)) as Reward;
    }

    public async satisfyRequirements(rewardId: number): Promise<Reward> {
        await this.transaction(async manager => {
            await manager.createQueryBuilder(RewardRequirementEntity, 'requirement')
                .update(RewardRequirementEntity)
                .set({ satisfied: true })
                .where('rewardId = :rewardId', { rewardId })
                .execute();

            await manager.update(RewardEntity, rewardId, {
                updateTime: () => 'CURRENT_TIMESTAMP'
            });
        });

        return await this.primary(r => r.get(rewardId)) as Reward;
    }

    public async updateContributor(rewardId: number, update: RewardContributorUpdate): Promise<Reward> {
        const entity = this.contributorMapper.updateToEntity(update);

        await this.transaction(async manager => {
            await manager.update(RewardContributorEntity, update.id, entity);
            await manager.update(RewardEntity, rewardId, {
                updateTime: () => 'CURRENT_TIMESTAMP'
            });
        });

        return await this.primary(r => r.get(rewardId)) as Reward;
    }

    public async satisfyContributors(rewardId: number): Promise<Reward> {
        await this.transaction(async manager => {
            await manager.createQueryBuilder(RewardContributorEntity, 'requirement')
                .update(RewardContributorEntity)
                .set({ satisfied: true })
                .where('rewardId = :rewardId', { rewardId })
                .execute();

            await manager.update(RewardEntity, rewardId, {
                updateTime: () => 'CURRENT_TIMESTAMP'
            });
        });

        return await this.primary(r => r.get(rewardId)) as Reward;
    }

    public async getCountByTemplateId(id: number): Promise<number> {
        const manager = await this.getManager();
        return manager.count(RewardEntity, {
            where: { templateId: id }
        });
    }

    public async getAll(filter: RewardFilter): Promise<PagedResult<Reward>> {
        const manager = await this.getManager();
        const query = manager
            .createQueryBuilder(RewardEntity, 'reward')
            .innerJoinAndMapOne('reward.template', RewardTemplateEntity, 'template', 'reward.templateId = template.id')
            .innerJoinAndMapOne('reward.user', UserEntity, 'user', 'reward.userId = user.id')
            .leftJoinAndMapMany('reward.requirements', RewardRequirementEntity, 'requirement', 'requirement.rewardId = reward.id')
            .leftJoinAndMapMany('reward.contributors', RewardContributorEntity, 'contributor', 'contributor.rewardId = reward.id');

        if (filter.userId)
            query.andWhere('reward.userId = :userId', { userId: filter.userId });

        if (filter.type)
            query.andWhere('reward.type IN (:...types)', { types: filter.type });

        if (filter.sourceType)
            query.andWhere('reward.sourceType IN (:...sourceType)', { sourceType: filter.sourceType });

        if (filter.excludeSourceType && filter.excludeSourceType.length > 0)
            query.andWhere('reward.sourceType NOT IN (:...excludeSourceType)', { excludeSourceType: filter.excludeSourceType });

        if (filter.redemptionType)
            query.andWhere('template.redemption = :redemptionType', { redemptionType: filter.redemptionType });

        if (filter.status && filter.status.length > 0)
            query.andWhere('reward.status IN (:...statuses)', { statuses: filter.status });

        if (filter.awardedFrom)
            query.andWhere('reward.createTime >= :createdFrom', { createdFrom: filter.awardedFrom });

        if (filter.awardedTo)
            query.andWhere('reward.createTime <= :createdTo', { createdTo: filter.awardedTo });

        if (filter.templateIdentifier)
            query.andWhere(`template.identifier = :templateIdentifier `, { templateIdentifier: filter.templateIdentifier });

        if (filter.templateName)
            query.andWhere(`template.name LIKE :templateName `, { templateName: `%${filter.templateName}%` });

        if (filter.templateId)
            query.andWhere('reward.templateId = :templateId', { templateId: filter.templateId });

        if (filter.requirement)
            query
                .innerJoin('reward.requirements', 'req', 'req.rewardId = reward.id')
                .andWhere('req.type IN (:...type)', { type: filter.requirement });

        if (filter.contributor)
            query
                .innerJoin('reward.contributors', 'cont', 'cont.rewardId = reward.id')
                .andWhere('cont.type IN (:...type)', { type: filter.contributor });

        if (filter.expiryTime)
            query.andWhere('reward.expireTime <= :expiryTime', { expiryTime: filter.expiryTime });

        if (filter.page && filter.pageSize) {
            query.skip((filter.page - 1) * filter.pageSize);
            query.take(filter.pageSize);
        }

        if (filter.order?.status)
            query
                .addSelect(`CASE WHEN reward.status = 'Pending' THEN 1 ELSE -1 END`, '_rank')
                .addOrderBy('_rank', 'DESC')
                .addOrderBy('reward.id', 'DESC');
        else if (filter.order)
            query.orderBy(convertOrdering('reward', filter.order));
        else
            query.addOrderBy('reward.id', 'DESC');

        const [entities, count] = await query.getManyAndCount();
        const page = filter?.page || 1;
        const pageSize = filter?.pageSize || count;
        const awards = entities.map(a => this.rewardMapper.fromEntity(a));

        return new PagedResult(awards, count, page, pageSize);
    }

    public async getPending(userId: number): Promise<Reward[]> {
        const manager = await this.getManager();
        const options: FindManyOptions<RewardEntity> = {
            relations: [
                'user',
                'template',
                'requirements',
                'contributors'
            ],
            where: {
                userId,
                status: RewardStatus.Pending
            }
        };

        const entities = await manager.find(RewardEntity, options);
        return entities.map(a => this.rewardMapper.fromEntity(a));
    }

    public async getSummaryForUser(userId: number, templateId: number): Promise<RewardSummary> {
        const manager = await this.getManager();
        const query = `
            SELECT
                templateId, count(templateId) as total,
                MIN(createTime) as firstAward,
                MAX(createTime) as lastAward
            FROM
                reward
            WHERE
                userId = ${userId}
            AND
                templateId = ${templateId}
            AND
                status NOT IN ('Cancelled', 'Failed')
            GROUP BY
                templateId
        `;

        const results = await manager.query(query);
        if (!results || results.length === 0)
            return {
                templateId,
                total: 0
            };

        const result = results[0];

        const transformer = new NumericTransformer();
        return {
            templateId: result.templateId,
            total: transformer.from(result.total) || 0,
            firstAward: result.firstAward ? moment(result.firstAward).toDate() : undefined,
            lastAward: result.lastAward ? moment(result.lastAward).toDate() : undefined
        };
    }

    public async getAllSummariesForUser(userId: number): Promise<RewardSummary[]> {
        const manager = await this.getManager();
        const query = `
            SELECT
                templateId, count(templateId) as total,
                MIN(createTime) as firstAward,
                MAX(createTime) as lastAward
            FROM
                reward
            WHERE
                userId = ${userId}
            AND
                status NOT IN ('Cancelled', 'Failed')
            GROUP BY
                templateId
        `;

        const result = await manager.query(query);
        if (!result || result.length === 0)
            return [];

        const transformer = new NumericTransformer();
        return result.map((s: any) => {
            return {
                templateId: s.templateId,
                total: transformer.from(s.total),
                firstAward: s.firstAward ? moment(s.firstAward).toDate() : undefined,
                lastAward: s.lastAward ? moment(s.lastAward).toDate() : undefined
            };
        });
    }

    public async getRewardSummariesForUser(userId: number, dateFrom: Date, dateTo: Date): Promise<{ [key: string]: string; }[]> {
        const manager = await this.getManager();
        const from = moment(dateFrom).startOf('day').format('YYYY-MM-DD HH:mm:ss');
        const to = moment(dateTo).endOf('day').format('YYYY-MM-DD HH:mm:ss');
        const query = `
            SELECT
                DATE(createTime) AS day,
                ROUND(SUM(CASE WHEN sourceType = 'WinBack' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS claimedWinBack,
                ROUND(SUM(CASE WHEN sourceType = 'WinBack' AND status = 'Completed' AND expireTime > CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS unclaimedWinBack,
                ROUND(SUM(CASE WHEN sourceType = 'WinBack' AND status = 'Completed' AND expireTime <= CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS expiredWinBack,
                ROUND(SUM(CASE WHEN sourceType = 'WinBack' AND status IN('Completed', 'Claimed') THEN sourceAmount ELSE 0 END), 2) AS totalWinBack,

                ROUND(SUM(CASE WHEN sourceType = 'DailyReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2)AS claimedDaily,
                ROUND(SUM(CASE WHEN sourceType = 'DailyReward' AND status = 'Completed' AND expireTime > CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2)AS unclaimedDaily,
                ROUND(SUM(CASE WHEN sourceType = 'DailyReward' AND status = 'Completed' AND expireTime <= CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS expiredDaily,
                ROUND(SUM(CASE WHEN sourceType = 'DailyReward' AND status IN('Completed', 'Claimed') THEN sourceAmount ELSE 0 END), 2) AS totalDailyReward,

                ROUND(SUM(CASE WHEN sourceType = 'WeeklyReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS claimedWeekly,
                ROUND(SUM(CASE WHEN sourceType = 'WeeklyReward' AND status = 'Completed' AND expireTime > CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS unclaimedWeekly,
                ROUND(SUM(CASE WHEN sourceType = 'WeeklyReward' AND status = 'Completed' AND expireTime <= CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS expiredWeekly,
                ROUND(SUM(CASE WHEN sourceType = 'WeeklyReward' AND status IN('Completed', 'Claimed') THEN sourceAmount ELSE 0 END), 2) AS totalWeeklyReward,

                ROUND(SUM(CASE WHEN sourceType = 'MonthlyReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS claimedMonthly,
                ROUND(SUM(CASE WHEN sourceType = 'MonthlyReward' AND status = 'Completed' AND expireTime > CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS unclaimedMonthly,
                ROUND(SUM(CASE WHEN sourceType = 'MonthlyReward' AND status = 'Completed' AND expireTime <= CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS expiredMonthly,
                ROUND(SUM(CASE WHEN sourceType = 'MonthlyReward' AND status IN('Completed', 'Claimed') THEN sourceAmount ELSE 0 END), 2) AS totalMonthlyReward,

                ROUND(SUM(CASE WHEN sourceType = 'RankUpReward' AND status = 'Claimed' THEN sourceAmount ELSE 0 END), 2) AS claimedRankUp,
                ROUND(SUM(CASE WHEN sourceType = 'RankUpReward' AND status = 'Completed' AND expireTime > CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS unclaimedRankUp,
                ROUND(SUM(CASE WHEN sourceType = 'RankUpReward' AND status = 'Completed' AND expireTime <= CURRENT_DATE() THEN sourceAmount ELSE 0 END), 2) AS expiredRankUp,
                ROUND(SUM(CASE WHEN sourceType = 'RankUpReward' AND status IN('Completed', 'Claimed') THEN sourceAmount ELSE 0 END), 2) AS totalRankUpReward,

                ROUND(SUM(CASE WHEN sourceType IN('WinBack', 'DailyReward', 'WeeklyReward', 'MonthlyReward', 'RankUpReward' ) AND status IN('Completed', 'Claimed') THEN sourceAmount ELSE 0 END), 2) AS overall
            FROM
                reward
            WHERE
                userId = ${userId}
            AND
                createTime >= '${from}'
            AND
                createTime <= '${to}'
            GROUP BY day
        `;

        const result = await manager.query(query);
        const bigNumberTransformer = new BigNumberTransformer();

        return result.map((row: Record<string, string>) => {
            const transformed: { [key: string]: BigNumber | string; } = { day: moment(row.day).format('YYYY-MM-DD') };
            for (const [key, value] of Object.entries(row))
                if (key && key !== 'day')
                    transformed[key] = bigNumberTransformer.from(value, 2) as BigNumber;

            return transformed;
        });
    }

    public async getAllByBalanceId(balanceId: number): Promise<Reward[]> {
        const manager = await this.getManager();
        const entities = await manager.find(RewardEntity, {
            where: { balanceId },
            relations: ['user']
        });

        return entities.map(a => this.rewardMapper.fromEntity(a));
    }
}