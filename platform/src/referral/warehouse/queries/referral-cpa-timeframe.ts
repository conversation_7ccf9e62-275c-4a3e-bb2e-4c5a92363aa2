export const REFERRAL_CPA_TIMEFRAME = `
    SELECT
        t.referrerUserId AS "referrerUserId",
        t.referrerDisplayName AS "referrerDisplayName",
        t.referrerReferralCount AS "referrerReferralCount",
        t.refereeUserId AS "refereeUserId",
        SUM(t.usdWagering) AS "usdWagering",
        SUM(t.usdWageringAdjusted) AS "usdWageringAdjusted",
        t.referralId AS "referralId",
        t.referredOn AS "referredOn"
    FROM
        (
        SELECT
            totals.refereeUserId,
            totals.referrerUserId,
            u.displayName AS referrerDisplayName,
            ru.referralCount AS referrerReferralCount,
            totals.usdWagering,
            totals.usdWageringAdjusted,
            totals.referredOn AS referredOn,
            totals.referralId AS referralId
        FROM
            (
            SELECT
                ref_period.refereeUserId AS refereeUserId,
                ref_period.referrerUserId AS referrerUserId,
                ref_period.referrealId AS referralId,
                ref_period.referredOn AS referredOn,
                SUM(ref_period.usdWagering) AS usdWagering,
                SUM(ref_period.usdWageringAdjusted) AS usdWageringAdjusted
            FROM
                (
                SELECT
                    gpas.userId AS userId,
                    gpas.buyInEffectiveAmount AS sourceWagering,
                    ROUND((gpas.buyInEffectiveAmount * cr.exchangeRate), 2) AS usdWagering,
                    g.id AS gameId,
                    CASE
                        WHEN g.metadata ilike '%cpaWageringMultiplier%' THEN ROUND((gpas.buyInEffectiveAmount * cr.exchangeRate) * PARSE_JSON(g.metadata):"cpaWageringMultiplier"::FLOAT, 2)
                        WHEN gpas.gameId IN (132, 1057, 1022) THEN ROUND((gpas.buyInEffectiveAmount * cr.exchangeRate) * 0.25, 2) -- Plinko games + stockmarket dev
                        WHEN g.rtp >= 99 THEN ROUND((gpas.buyInEffectiveAmount * cr.exchangeRate) * 0.25, 2) -- Games RTP
                        WHEN g.typeId IN (7,11,12,13) THEN ROUND((gpas.buyInEffectiveAmount * cr.exchangeRate) * 0.5, 2) -- Live games
                        ELSE ROUND((gpas.buyInEffectiveAmount * cr.exchangeRate), 2) -- Default case
                    END AS usdWageringAdjusted,
                    qualified_users.referrerUserId AS referrerUserId,
                    qualified_users.refereeUserId AS refereeUserId,
                    qualified_users.referredOn AS referredOn,
                    qualified_users.id AS referrealId
                FROM
                    (
                    SELECT
                        DISTINCT(r.refereeUserId),
                        r.referrerUserId,
                        r.createtime AS referredOn,
                        r.id
                    FROM
                        fivetran_cdc_platform.referral r
                    INNER JOIN
                        fivetran_cdc_platform.user u ON u.id = r.refereeUserId
                    INNER JOIN
                        fivetran_cdc_platform.referral_user ru ON ru.userId = r.refereeUserId
                    WHERE
                        r.createTime >= :dateFrom
                    AND
                        r.createTime <= :dateTo
                    AND
                        u.type = 'Standard'
                    AND
                        u.enabled = 1
                    AND
                        ru.qualified = 0
                    AND
                        r.enabled = 1
                    ) AS qualified_users
                INNER JOIN
                    fivetran_cdc_platform.game_play_action_statistics gpas ON gpas.userId = qualified_users.refereeUserId
                INNER JOIN
                    fivetran_cdc_platform.game g ON g.id = gpas.gameId
                INNER JOIN
                    (
                    SELECT
                        date,
                        source,
                        exchangeRate
                    FROM
                        fivetran_cdc_platform.currency_rate
                    WHERE
                        target = 'USD'
                    ) AS cr ON cr.date = DATE(gpas.createTime) AND cr.source = gpas.currencycode
                ) AS ref_period
                GROUP BY
                    referrerUserId,
                    refereeUserId,
                    ref_period.referrealId,
                    ref_period.referredOn
                HAVING
                    SUM(ref_period.usdWageringAdjusted) >= :wageredFrom
            ) AS totals
            INNER JOIN
                fivetran_cdc_platform.referral_user ru ON ru.userId = totals.referrerUserId
            INNER JOIN
                fivetran_cdc_platform.user u ON u.id = ru.userId
            WHERE
                u.type = 'Standard'
            AND
                u.enabled = 1
            AND
                ru.affiliate = 0
            AND
                ru.active = 1
        ) AS t
    GROUP BY
        t.referrerUserId,
        t.refereeUserId,
        t.referrerDisplayName,
        t.referrerReferralCount,
        t.referralId,
        t.referredOn;
`;