export const REFERRAL_USER_STATISTICS_BY_ID = `
SELECT
    RU.userId as "userId",
    RU.clickCount as "clickCount",
    RU.referralCount as "referralCount",
    RU.revenue as "revenue",
    RU.rewardCount as "rewardCount",
    IFNULL(RCTD.clickCount, 0) as "clickCountToday",
    IFNULL(RCYD.clickCount, 0) as "clickCountYesterday",
    IFNULL(RC7D.clickCount, 0) as "clickCount7Days",
    IFNULL(RCTM.clickCount, 0) as "clickCountThisMonth",
    IFNULL(RCLM.clickCount, 0) as "clickCountLastMonth",
    IFNULL(RTD.count, 0) as "referralCountToday",
    IFNULL(RYD.count, 0) as "referralCountYesterday",
    IFNULL(R7D.count, 0) as "referralCount7Days",
    IFNULL(RTM.count, 0) as "referralCountThisMonth",
    IFNULL(RLM.count, 0) as "referralCountLastMonth",
    IFNULL(RWTD.count, 0) as "rewardCountToday",
    IFNULL(RWYD.count, 0) as "rewardCountYesterday",
    IFNULL(RW7D.count, 0) as "rewardCount7Days",
    IFNULL(RWTM.count, 0) as "rewardCountThisMonth",
    (IFNULL(RWLM.count, 0) + IFNULL(NGRRWLM.count, 0)) as "rewardCountLastMonth",
    IFNULL(RWTD.commission, 0) as "revenueToday",
    IFNULL(RWYD.commission, 0) as "revenueYesterday",
    IFNULL(RW7D.commission, 0) as "revenue7Days",
    IFNULL(RWTM.commission, 0) as "revenueThisMonth",
    (IFNULL(RWLM.commission, 0) + IFNULL(NGRRWLM.commission, 0)) as "revenueLastMonth",
    IFNULL(FDTD.count, 0) as "firstDepositCountToday",
    IFNULL(FDYD.count, 0) as "firstDepositCountYesterday",
    IFNULL(FD7D.count, 0) as "firstDepositCount7Days",
    IFNULL(FDTM.count, 0) as "firstDepositCountThisMonth",
    IFNULL(FDLM.count, 0) as "firstDepositCountLastMonth",
    IFNULL(NRTD.amountBase, 0) as "ngrToday",
    IFNULL(NRYD.amountBase, 0) as "ngrYesterday",
    IFNULL(NR7D.amountBase, 0) as "ngr7Days",
    IFNULL(NRTM.amountBase, 0) as "ngrThisMonth",
    IFNULL(NRLM.amountBase, 0) as "ngrLastMonth"
FROM
    fivetran_cdc_platform.referral_user RU
LEFT JOIN
    (
        SELECT
            RT.referrerUserId as userId,
            SUM(RC.amount) clickCount
        FROM
            fivetran_cdc_platform.referral_click RC
        INNER JOIN
            fivetran_cdc_platform.referral_tracker RT ON RT.id = RC.trackerId
        WHERE
            RC.date = CURRENT_DATE()
        GROUP BY
            RT.referrerUserId
    ) RCTD ON RCTD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            RT.referrerUserId as userId,
            SUM(RC.amount) clickCount
        FROM
            fivetran_cdc_platform.referral_click RC
        INNER JOIN
            fivetran_cdc_platform.referral_tracker RT ON RT.id = RC.trackerId
        WHERE
            RC.date = DATEADD(DAY, -1, CURRENT_DATE())
        GROUP BY
            RT.referrerUserId
    ) RCYD ON RCYD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            RT.referrerUserId as userId,
            SUM(RC.amount) clickCount
        FROM
            fivetran_cdc_platform.referral_click RC
        INNER JOIN
            fivetran_cdc_platform.referral_tracker RT ON RT.id = RC.trackerId
        WHERE
            RC.date BETWEEN DATEADD(DAY, -7, CURRENT_DATE()) AND CURRENT_DATE()
        GROUP BY
            RT.referrerUserId
    ) RC7D ON RC7D.userId = RU.userId
LEFT JOIN
    (
        SELECT
            RT.referrerUserId as userId,
            SUM(RC.amount) clickCount
        FROM
            fivetran_cdc_platform.referral_click RC
        INNER JOIN
            fivetran_cdc_platform.referral_tracker RT ON RT.id = RC.trackerId
        WHERE
            RC.date BETWEEN DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE()) AND LAST_DAY(CURRENT_DATE())
        GROUP BY
            RT.referrerUserId
    ) RCTM ON RCTM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            RT.referrerUserId as userId,
            SUM(RC.amount) clickCount
        FROM
            fivetran_cdc_platform.referral_click RC
        INNER JOIN
            fivetran_cdc_platform.referral_tracker RT ON RT.id = RC.trackerId
        WHERE
            RC.date BETWEEN DATEADD(MONTH, -1, DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE())) AND LAST_DAY(DATEADD(MONTH, -1, CURRENT_DATE()))
        GROUP BY
            RT.referrerUserId
    ) RCLM ON RCLM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(R.id) count
        FROM
            fivetran_cdc_platform.referral R
        WHERE
            R.enabled = 1
        AND
            DATE(R.createTime) = CURRENT_DATE()
        GROUP BY
            R.referrerUserId
    ) RTD ON RTD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(R.id) count
        FROM
            fivetran_cdc_platform.referral R
        WHERE
            R.enabled = 1
        AND
            DATE(R.createTime) = DATEADD(DAY, -1, CURRENT_DATE())
        GROUP BY
            R.referrerUserId
    ) RYD ON RYD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(R.id) count
        FROM
            fivetran_cdc_platform.referral R
        WHERE
            R.enabled = 1
        AND
            DATE(R.createTime) BETWEEN DATEADD(DAY, -7, CURRENT_DATE()) AND CURRENT_DATE()
        GROUP BY
            R.referrerUserId
    ) R7D ON R7D.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(R.id) count
        FROM
            fivetran_cdc_platform.referral R
        WHERE
            R.enabled = 1
        AND
            DATE(R.createTime) BETWEEN DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE()) AND LAST_DAY(CURRENT_DATE())
        GROUP BY
            R.referrerUserId
    ) RTM ON RTM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(R.id) count
        FROM
            fivetran_cdc_platform.referral R
        WHERE
            R.enabled = 1
        AND
            DATE(R.createTime) BETWEEN DATEADD(MONTH, -1, DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE())) AND LAST_DAY(DATEADD(MONTH, -1, CURRENT_DATE()))
        GROUP BY
            R.referrerUserId
    ) RLM ON RLM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(RW.id) count,
            SUM(RW.commission) commission
        FROM
            fivetran_cdc_platform.referral_reward RW
        INNER JOIN
            fivetran_cdc_platform.referral R ON R.id = RW.referralId AND R.enabled = 1
        WHERE
            RW.type = 'Commission'
        AND
            RW.event != 'NetGamingRevenue'
        AND
            DATE(RW.createTime) = CURRENT_DATE()
        GROUP BY
            R.referrerUserId
    ) RWTD ON RWTD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(RW.id) count,
            SUM(RW.commission) commission
        FROM
            fivetran_cdc_platform.referral_reward RW
        INNER JOIN
            fivetran_cdc_platform.referral R ON R.id = RW.referralId AND R.enabled = 1
        WHERE
            RW.type = 'Commission'
        AND
            RW.event != 'NetGamingRevenue'
        AND
            DATE(RW.createTime) = DATEADD(DAY, -1, CURRENT_DATE())
        GROUP BY
            R.referrerUserId
    ) RWYD ON RWYD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(RW.id) count,
            SUM(RW.commission) commission
        FROM
            fivetran_cdc_platform.referral_reward RW
        INNER JOIN
            fivetran_cdc_platform.referral R ON R.id = RW.referralId AND R.enabled = 1
        WHERE
            RW.type = 'Commission'
        AND
            RW.event != 'NetGamingRevenue'
        AND
            DATE(RW.createTime) BETWEEN DATEADD(DAY, -7, CURRENT_DATE()) AND CURRENT_DATE()
        GROUP BY
            R.referrerUserId
    ) RW7D ON RW7D.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(RW.id) count,
            SUM(RW.commission) commission
        FROM
            fivetran_cdc_platform.referral_reward RW
        INNER JOIN
            fivetran_cdc_platform.referral R ON R.id = RW.referralId AND R.enabled = 1
        WHERE
            RW.type = 'Commission'
        AND
            RW.event != 'NetGamingRevenue'
        AND
            DATE(RW.createTime) BETWEEN DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE()) AND LAST_DAY(CURRENT_DATE())
        GROUP BY
            R.referrerUserId
    ) RWTM ON RWTM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(RW.id) count,
            SUM(RW.commission) commission
        FROM
            fivetran_cdc_platform.referral_reward RW
        INNER JOIN
            fivetran_cdc_platform.referral R ON R.id = RW.referralId AND R.enabled = 1
        WHERE
            RW.type = 'Commission'
        AND
            RW.event != 'NetGamingRevenue'
        AND
            DATE(RW.createTime) BETWEEN DATEADD(MONTH, -1, DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE())) AND LAST_DAY(DATEADD(MONTH, -1, CURRENT_DATE()))
        GROUP BY
            R.referrerUserId
    ) RWLM ON RWLM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(RW.id) count,
            SUM(RW.commission) commission
        FROM
            fivetran_cdc_platform.referral_reward RW
        INNER JOIN
            fivetran_cdc_platform.referral R ON R.id = RW.referralId AND R.enabled = 1
        WHERE
            RW.type = 'Commission'
        AND
            RW.event = 'NetGamingRevenue'
        AND
            DATEADD(MONTH, -1, RW.createTime) BETWEEN DATEADD(MONTH, -1, DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE())) AND LAST_DAY(DATEADD(MONTH, -1, CURRENT_DATE()))
        GROUP BY
            R.referrerUserId
    ) NGRRWLM ON NGRRWLM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(P.id) count
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.payment P ON P.userId = R.refereeUserId
        INNER JOIN
            (
                SELECT
                    P.id,
                    P.userId userId,
                    MIN(P.createTime) as date
                FROM
                    fivetran_cdc_platform.payment P
                WHERE
                    P.status = 'Successful'
                AND
                    P.type = 'Deposit'
                GROUP BY
                    P.userId, P.id
            ) D ON D.id = P.id AND DATE(D.date) = CURRENT_DATE()
        WHERE
            R.enabled = 1
        GROUP BY
            R.referrerUserId
    ) FDTD ON FDTD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(P.id) count
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.payment P ON P.userId = R.refereeUserId
        INNER JOIN
            (
                SELECT
                    P.id,
                    P.userId userId,
                    MIN(P.createTime) as date
                FROM
                    fivetran_cdc_platform.payment P
                WHERE
                    P.status = 'Successful'
                AND
                    P.type = 'Deposit'
                GROUP BY
                    P.userId, P.id
            ) D ON D.id = P.id AND DATE(D.date) = DATEADD(DAY, -1, CURRENT_DATE())
        WHERE
            R.enabled = 1
        GROUP BY
            R.referrerUserId
    ) FDYD ON FDYD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(P.id) count
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.payment P ON P.userId = R.refereeUserId
        INNER JOIN
            (
                SELECT
                    P.id,
                    P.userId userId,
                    MIN(P.createTime) as date
                FROM
                    fivetran_cdc_platform.payment P
                WHERE
                    P.status = 'Successful'
                AND
                    P.type = 'Deposit'
                GROUP BY
                    P.userId, P.id
            ) D ON D.id = P.id AND DATE(D.date) BETWEEN DATEADD(DAY, -7, CURRENT_DATE()) AND CURRENT_DATE()
        WHERE
            R.enabled = 1
        GROUP BY
            R.referrerUserId
    ) FD7D ON FD7D.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(P.id) count
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.payment P ON P.userId = R.refereeUserId
        INNER JOIN
            (
                SELECT
                    P.id,
                    P.userId userId,
                    MIN(P.createTime) as date
                FROM
                    fivetran_cdc_platform.payment P
                WHERE
                    P.status = 'Successful'
                AND
                    P.type = 'Deposit'
                GROUP BY
                    P.userId, P.id
            ) D ON D.id = P.id AND DATE(D.date) BETWEEN DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE()) AND LAST_DAY(CURRENT_DATE())
        WHERE
            R.enabled = 1
        GROUP BY
            R.referrerUserId
    ) FDTM ON FDTM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            COUNT(P.id) count
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.payment P ON P.userId = R.refereeUserId
        INNER JOIN
            (
                SELECT
                    P.id,
                    P.userId userId,
                    MIN(P.createTime) as date
                FROM
                    fivetran_cdc_platform.payment P
                WHERE
                    P.status = 'Successful'
                AND
                    P.type = 'Deposit'
                GROUP BY
                    P.userId, P.id
            ) D ON D.id = P.id AND DATE(D.date) BETWEEN DATEADD(MONTH, -1, DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE())) AND LAST_DAY(DATEADD(MONTH, -1, CURRENT_DATE()))
        WHERE
            R.enabled = 1
        GROUP BY
            R.referrerUserId
    ) FDLM ON FDLM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            SUM(((GPAS.effectiveRevenue * CR.exchangeRate) * :ngrMultiplier) * -1) amountBase
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.game_play_action_statistics GPAS ON GPAS.userId = R.refereeUserId
        LEFT JOIN
            (
                SELECT
                    CR.exchangeRate,
                    CR.source
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
                AND
                    CR.date = CURRENT_DATE()
            ) AS CR ON CR.source = GPAS.currencyCode
        WHERE
            R.enabled = 1
        AND
            DATE(GPAS.date) = CURRENT_DATE()
        GROUP BY
            R.referrerUserId
    ) NRTD ON NRTD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            SUM(((GPAS.effectiveRevenue * CR.exchangeRate) * :ngrMultiplier) * -1) amountBase
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.game_play_action_statistics GPAS ON GPAS.userId = R.refereeUserId
        LEFT JOIN
            (
                SELECT
                    CR.exchangeRate,
                    CR.source
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
                AND
                    CR.date = CURRENT_DATE()
            ) AS CR ON CR.source = GPAS.currencyCode
        WHERE
            R.enabled = 1
        AND
            DATE(GPAS.date) = DATEADD(DAY, -1, CURRENT_DATE())
        GROUP BY
            R.referrerUserId
    ) NRYD ON NRYD.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            SUM(((GPAS.effectiveRevenue * CR.exchangeRate) * :ngrMultiplier) * -1) amountBase
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.game_play_action_statistics GPAS ON GPAS.userId = R.refereeUserId
        LEFT JOIN
            (
                SELECT
                    CR.exchangeRate,
                    CR.source
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
                AND
                    CR.date = CURRENT_DATE()
            ) AS CR ON CR.source = GPAS.currencyCode
        WHERE
            R.enabled = 1
        AND
            DATE(GPAS.date) BETWEEN DATEADD(DAY, -7, CURRENT_DATE()) AND CURRENT_DATE()
        GROUP BY
            R.referrerUserId
    ) NR7D ON NR7D.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            SUM(((GPAS.effectiveRevenue * CR.exchangeRate) * :ngrMultiplier) * -1) amountBase
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.game_play_action_statistics GPAS ON GPAS.userId = R.refereeUserId
        LEFT JOIN
            (
                SELECT
                    CR.exchangeRate,
                    CR.source
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
                AND
                    CR.date = CURRENT_DATE()
            ) AS CR ON CR.source = GPAS.currencyCode
        WHERE
            R.enabled = 1
        AND
            DATE(GPAS.date) BETWEEN DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE()) AND LAST_DAY(CURRENT_DATE())
        GROUP BY
            R.referrerUserId
    ) NRTM ON NRTM.userId = RU.userId
LEFT JOIN
    (
        SELECT
            R.referrerUserId as userId,
            SUM(((GPAS.effectiveRevenue * CR.exchangeRate) * :ngrMultiplier) * -1) amountBase
        FROM
            fivetran_cdc_platform.referral R
        INNER JOIN
            fivetran_cdc_platform.game_play_action_statistics GPAS ON GPAS.userId = R.refereeUserId
        LEFT JOIN
            (
                SELECT
                    CR.exchangeRate,
                    CR.source
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
                AND
                    CR.date = CURRENT_DATE()
            ) AS CR ON CR.source = GPAS.currencyCode
        WHERE
            R.enabled = 1
        AND
            DATE(GPAS.date) BETWEEN DATEADD(MONTH, -1, DATEADD(DAY, -(DAYOFMONTH(CURRENT_DATE())-1), CURRENT_DATE())) AND LAST_DAY(DATEADD(MONTH, -1, CURRENT_DATE()))
        GROUP BY
            R.referrerUserId
    ) NRLM ON NRLM.userId = RU.userId
WHERE
    RU.userId = :userId
`;
