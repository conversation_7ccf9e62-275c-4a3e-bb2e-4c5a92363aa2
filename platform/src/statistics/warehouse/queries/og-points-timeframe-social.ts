export const OG_POINTS_TIMEFRAME_SOCIAL = `
select
    lp.userId as "userId",
    0 as "sweepPoints",
    0 as "crashPlinkoPoints",
    0 as "wageringPoints",
    0 as "livePoints",
    0 as "miniGamePoints",
    lp.totalPoints as "totalPoints",
    ROW_NUMBER() OVER (ORDER BY lp.totalpoints DESC) as "totalPointsRank"
from (
    select le.userId, sum(le.points) as totalpoints
    from
        fivetran_cdc_platform.leaderboard_entry le
    inner join
        fivetran_cdc_platform.leaderboard  l on l.id = le.leaderboardid
    inner join
        fivetran_cdc_platform.user u on u.id = le.userid
    where
        PARSE_JSON(l.metadata):socialProvider = 'Twitter'
    and
        le.points > 0
    and
        (:dateFrom IS NULL OR l.createTime >= :dateFrom)
    and
        (:dateTo IS NULL OR l.createTime <= :dateTo)
    and
        u.type = 'Standard'
    and
        u.enabled = 1
    group by userId
) lp
`;