export const GET_TOTALS = `
SELECT
    R.revenue as "revenue",
    TL.liability as "liability",
    WRP.pendingWithdrawals as "pendingWithdrawals",
    WRPD.pendingWithdrawals as "pendingDefaultWithdrawals",
    WRPC.pendingWithdrawals as "pendingCustodialWithdrawals",
    WRC.completedWithdrawals as "completedWithdrawals",
    WRCD.completedWithdrawals as "completedDefaultWithdrawals",
    WRCC.completedWithdrawals as "completedCustodialWithdrawals",
    U.totalSignUps as "totalSignUps",
    BI.buyIns as "buyIns",
    W.winnings as "winnings",
    D.depositRevenue as "depositRevenue",
    PC.totalPayments as "totalPayments",
    DC.totalDeposits as "totalDeposits",
    DCD.totalDeposits as "totalDefaultDeposits",
    DCC.totalDeposits as "totalCustodialDeposits",
    S.sweepstakesCompleted as "sweepstakesCompleted",
    S.sweepstakesActive as "sweepstakesActive",
    S.sweepstakesEntries as "sweepstakesEntries",
    SW.sweepstakeUsers as "sweepstakeUsers",
    SW.sweepstakesWinners as "sweepstakesWinners",
    SEPV.sweepstakesPrizeValue as "sweepstakesPrizeValue"
FROM
(
    (
        SELECT ROUND(SUM(rev.revenueAmount * rates.exchangeRate), 2) as revenue
        FROM (
            SELECT IFNULL(SUM(P.amount), 0) as revenueAmount, P.currencyCode
            FROM fivetran_cdc_platform.payment P
            INNER JOIN fivetran_cdc_platform.user U ON U.id = P.userId
            WHERE U.type = 'Standard'
            AND P.status = 'Successful'
            AND P.direction = 'In'
            GROUP BY P.currencyCode
        ) rev
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) rates
        ON rates.source = rev.currencyCode
    )
) R,
(
    (
        SELECT ROUND(SUM(lia.balanceRaw * liabilityRates.exchangeRate), 2) as liability
        FROM (
            SELECT IFNULL(SUM(balanceRaw), 0) as balanceRaw, WA.currencyCode
            FROM fivetran_cdc_platform.wallet W
            INNER JOIN fivetran_cdc_platform.user U ON U.id = W.userId
            INNER JOIN fivetran_cdc_platform.wallet_account WA ON WA.walletId = W.id
            WHERE U.type = 'Standard'
            AND WA.name IN ('Withdrawable', 'Custodial', 'Escrow')
            GROUP BY WA.currencyCode
        ) as lia
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) liabilityRates
        ON liabilityRates.source = lia.currencyCode
    )
) TL,
(
    SELECT IFNULL(ROUND(SUM(pendingWithdrawals.amount * pendingWithdrawalRates.exchangeRate), 2), 0) as pendingWithdrawals
    FROM (
        SELECT IFNULL(ROUND(SUM(amount), 2), 0) as amount, currencyCode
        FROM fivetran_cdc_platform.withdrawal WR
        WHERE WR.status IN ('Pending', 'Approved', 'Confirmed', 'Processing','InsufficientFunds')
        GROUP BY WR.currencyCode
    ) pendingWithdrawals
    INNER JOIN (
        SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
    ) pendingWithdrawalRates
    ON pendingWithdrawalRates.source = pendingWithdrawals.currencyCode
) WRP,
(
    SELECT IFNULL(ROUND(SUM(pendingWithdrawals.amount * pendingWithdrawalRates.exchangeRate), 2), 0) as pendingWithdrawals
    FROM (
        SELECT IFNULL(ROUND(SUM(amount), 2), 0) as amount, currencyCode
        FROM fivetran_cdc_platform.withdrawal WR
        WHERE WR.status IN ('Pending', 'Approved', 'Confirmed', 'Processing','InsufficientFunds')
        AND (WR.fundsContext IS NULL OR WR.fundsContext = 'Default')
        GROUP BY WR.currencyCode
    ) pendingWithdrawals
    INNER JOIN (
        SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
    ) pendingWithdrawalRates
    ON pendingWithdrawalRates.source = pendingWithdrawals.currencyCode
) WRPD,
(
    SELECT IFNULL(ROUND(SUM(pendingWithdrawals.amount * pendingWithdrawalRates.exchangeRate), 2), 0) as pendingWithdrawals
    FROM (
        SELECT IFNULL(ROUND(SUM(amount), 2), 0) as amount, currencyCode
        FROM fivetran_cdc_platform.withdrawal WR
        WHERE WR.status IN ('Pending', 'Approved', 'Confirmed', 'Processing','InsufficientFunds')
        AND WR.fundsContext = 'Custodial'
        GROUP BY WR.currencyCode
    ) pendingWithdrawals
    INNER JOIN (
        SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
    ) pendingWithdrawalRates
    ON pendingWithdrawalRates.source = pendingWithdrawals.currencyCode
) WRPC,
(
    (
        SELECT IFNULL(ROUND(SUM(completedWithdrawals.amount * completedWithdrawalRates.exchangeRate), 2), 0) as completedWithdrawals
        FROM (
            SELECT IFNULL(SUM(amount),0) as amount, currencyCode
            FROM fivetran_cdc_platform.withdrawal WR WHERE WR.status = 'Complete'
            GROUP BY WR.currencyCode
        ) as completedWithdrawals
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) completedWithdrawalRates
        ON completedWithdrawalRates.source = completedWithdrawals.currencyCode
    )
) WRC,
(
    (
        SELECT IFNULL(ROUND(SUM(completedWithdrawals.amount * completedWithdrawalRates.exchangeRate), 2), 0) as completedWithdrawals
        FROM (
            SELECT IFNULL(SUM(amount),0) as amount, currencyCode
            FROM fivetran_cdc_platform.withdrawal WR
            WHERE WR.status = 'Complete'
            AND (WR.fundsContext IS NULL OR WR.fundsContext = 'Default')
            GROUP BY WR.currencyCode
        ) as completedWithdrawals
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) completedWithdrawalRates
        ON completedWithdrawalRates.source = completedWithdrawals.currencyCode
    )
) WRCD,
(
    (
        SELECT IFNULL(ROUND(SUM(completedWithdrawals.amount * completedWithdrawalRates.exchangeRate), 2), 0) as completedWithdrawals
        FROM (
            SELECT IFNULL(SUM(amount),0) as amount, currencyCode
            FROM fivetran_cdc_platform.withdrawal WR
            WHERE WR.status = 'Complete'
            AND WR.fundsContext = 'Custodial'
            GROUP BY WR.currencyCode
        ) as completedWithdrawals
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) completedWithdrawalRates
        ON completedWithdrawalRates.source = completedWithdrawals.currencyCode
    )
) WRCC,
(SELECT IFNULL(COUNT(id), 0) as totalSignUps FROM fivetran_cdc_platform.user U WHERE U.type = 'Standard') U,
(
    (
        SELECT ROUND(SUM(ABS(walletTrans.buyIns) * walletTransRates.exchangeRate), 2) as buyIns
        FROM (
            SELECT IFNULL(SUM(ABS(WT.amountRaw)), 0) as buyIns, WT.currencyCode
            FROM fivetran_cdc_platform.wallet_transaction WT
            JOIN fivetran_cdc_platform.wallet W ON WT.walletId = W.id
            JOIN fivetran_cdc_platform.user U ON W.userId = U.id
            WHERE U.type = 'Standard'
            AND WT.amountRaw < 0
            AND WT.purpose IN ('BuyIn')
            GROUP BY WT.currencyCode
        ) as walletTrans
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) walletTransRates
        ON walletTransRates.source = walletTrans.currencyCode
    )
) BI,
(
    (
        SELECT ROUND(SUM(win.winnings * winRates.exchangeRate), 2) as winnings
        FROM (
            SELECT IFNULL(SUM(WT.amountRaw), 0) as winnings, WT.currencyCode
            FROM fivetran_cdc_platform.wallet_transaction WT
            JOIN fivetran_cdc_platform.wallet W ON WT.walletId = W.id
            JOIN fivetran_cdc_platform.user U ON W.userId = U.id
            WHERE U.type = 'Standard'
            AND WT.amountRaw > 0
            AND WT.purpose IN ('PayOut', 'JackpotPayout')
            GROUP BY WT.currencyCode
        ) win
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) winRates
        ON winRates.source = win.currencyCode
    )
) W,
(
    (
        SELECT ROUND(SUM(pay.depositRevenue * payRates.exchangeRate), 2) as depositRevenue
        FROM (
            SELECT IFNULL(SUM(amount), 0) as depositRevenue, P.currencyCode
            FROM fivetran_cdc_platform.payment P
            INNER JOIN fivetran_cdc_platform.user U on U.id = P.userId
            WHERE U.type = 'Standard'
            AND P.status = 'Successful'
            AND P.type = 'Deposit'
            GROUP BY P.currencyCode
        ) pay
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) payRates
        ON payRates.source = pay.currencyCode
    )
) D,
(
    (
        SELECT IFNULL(COUNT(PC.id), 0) as totalPayments
        FROM fivetran_cdc_platform.payment PC
        INNER JOIN fivetran_cdc_platform.user U ON U.id = PC.userId
        WHERE U.type = 'Standard'
        AND PC.status = 'Successful'
        AND PC.direction = 'In'
    ) PC
),
(
    (
        SELECT IFNULL(COUNT(DC.id), 0) as totalDeposits
        FROM fivetran_cdc_platform.payment DC
        INNER JOIN fivetran_cdc_platform.user U on U.id = DC.userId
        WHERE U.type = 'Standard'
        AND DC.status = 'Successful'
        AND DC.type = 'Deposit'
    ) DC
),
(
    (
        SELECT IFNULL(COUNT(DC.id), 0) as totalDeposits
        FROM fivetran_cdc_platform.payment DC
        INNER JOIN fivetran_cdc_platform.user U on U.id = DC.userId
        WHERE U.type = 'Standard'
        AND DC.status = 'Successful'
        AND DC.type = 'Deposit'
        AND (DC.fundsContext IS NULL OR DC.fundsContext = 'Default')
    ) DCD
),
(
    (
        SELECT IFNULL(COUNT(DC.id), 0) as totalDeposits
        FROM fivetran_cdc_platform.payment DC
        INNER JOIN fivetran_cdc_platform.user U on U.id = DC.userId
        WHERE U.type = 'Standard'
        AND DC.status = 'Successful'
        AND DC.type = 'Deposit'
        AND DC.fundsContext = 'Custodial'
    ) DCC
),
(
    (
        SELECT IFNULL(SUM(CASE WHEN S.payoutTime IS NOT NULL THEN 1 ELSE NULL END), 0) as sweepstakesCompleted,
        IFNULL(SUM(CASE WHEN S.state IN (2, 3) AND (SP.id IS NULL OR SP.staked = 1) THEN 1 ELSE NULL END), 0) as sweepstakesActive,
        IFNULL(SUM(S.entryCount), 0) as sweepstakesEntries
        FROM fivetran_cdc_platform.sweepstake S
        LEFT JOIN fivetran_cdc_platform.sweepstake_prize SP ON SP.sweepstakeId = S.id AND S.type = 'Blockchain'
        WHERE S.public = 1
    ) S
),
(
    (
        SELECT IFNULL(COUNT(DISTINCT SE.userId), 0) as sweepstakeUsers,
        IFNULL(COUNT(DISTINCT CASE WHEN SE.payoutTime IS NOT NULL THEN SE.userId END), 0) as sweepstakesWinners
        FROM fivetran_cdc_platform.sweepstake_entry_set SE
        INNER JOIN fivetran_cdc_platform.sweepstake S ON S.id = SE.sweepstakeId
        WHERE S.public = 1
    ) SW
),
(
    (
        SELECT ROUND(SUM(entrySets.sweepstakesPrizeValue * entrySetsRates.exchangeRate), 2) as sweepstakesPrizeValue
        FROM (
            SELECT SUM(SEP.initialValue) as sweepstakesPrizeValue, S.currencyCode
            FROM fivetran_cdc_platform.sweepstake_entry_set SE
            INNER JOIN fivetran_cdc_platform.sweepstake_prize_award SEPA ON SEPA.entrySetId = SE.id
            INNER JOIN fivetran_cdc_platform.sweepstake_prize SEP ON SEP.id = SEPA.prizeId
            INNER JOIN fivetran_cdc_platform.sweepstake S ON S.id = SE.sweepstakeId
            WHERE SEP.type = 'NFT' AND S.public = 1
            GROUP BY S.currencyCode
        ) entrySets
        INNER JOIN (
            SELECT cr.date AS currentDate, cr.exchangeRate, cr.source, cr.target FROM fivetran_cdc_platform.currency_rate cr WHERE cr.target = :baseCurrency AND cr.date = date(current_timestamp)
        ) entrySetsRates
        ON entrySetsRates.source = entrySets.currencyCode
    )
) SEPV;
 `;