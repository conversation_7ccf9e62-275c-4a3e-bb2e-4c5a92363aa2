export const OG_POINTS_TIMEFRAME_RAF = `
select
    tp.userId as "userId",
    0 as "sweepPoints",
    0 as "crashPlinkoPoints",
    0 as "wageringPoints",
    0 as "livePoints",
    0 as "miniGamePoints",
    tp.totalPoints as "totalPoints",
    ROW_NUMBER() OVER (ORDER BY tp.totalpoints DESC, tp.earliestReferralDate ASC) as "totalPointsRank"
from (
select
   referreruserid as userId, sum(points) as totalpoints, min(referredOn) as earliestReferralDate
from
(
select
    referreruserId, refereeuserid, case when sum(usdwageringadjusted) > 1000 then 5000 else 0 end as points, min(referredOn) as referredOn
from
(
select
    ref_period.*,
    gpas.date,
    gpas.buyineffectiveamount as sourceWagering,
    gpas.currencycode as sourceCurrency,
    round((gpas.buyineffectiveamount * cr.exchangeRate), 2) as usdWagering,
    case
        when g.id in (489, 512, 502, 866, 670, 872, 3451) then round((gpas.buyineffectiveamount * cr.exchangeRate) * 0.25, 2) -- plinko / stockmarket
        when g.typeid = 7 then round((gpas.buyineffectiveamount * cr.exchangeRate) * 0.5, 2) -- live games
        when g.typeid in (2, 5) then round((gpas.buyineffectiveamount * cr.exchangeRate) * 0.25, 2) -- hilo / rekt
        else round((gpas.buyineffectiveamount * cr.exchangeRate), 2) -- all other games
    end as usdWageringAdjusted,
    case
        when g.id in (489, 512, 502, 866, 670, 872) then 'plinko'
        when g.id in (3451) then 'stockmarket'
        when g.typeid = 5 then 'crash'
        when g.typeid = 7 then 'live'
        when g.typeid = 2 then 'hilo'
        when g.typeid in (6, 8) then 'mini'
        else 'slots'
    end as wagering_type
from
(
    select
        distinct(r.refereeuserid), r.referreruserid, r.createtime as referredOn
    from
        fivetran_cdc_platform.referral r
    inner join
        fivetran_cdc_platform.user u on r.refereeuserid = u.id
    left join
        (select userId from fivetran_cdc_platform.user_tag where tag = 'Affiliate') at on at.userId = r.referreruserid -- exclude referrer with tag
    inner join
        fivetran_cdc_platform.user ur on r.referreruserid = ur.id
    where
        (:dateFrom IS NULL OR r.createTime >= :dateFrom)
    and
        (:dateTo IS NULL OR r.createTime <= :dateTo)
    and
        u.type = 'Standard'
    and
        u.deleted = 0
    and
        u.enabled = 1
    and
        ur.type = 'Standard'
    and
        ur.enabled = 1
    and
        at.userid is null
) ref_period
inner join
    fivetran_cdc_platform.game_play_action_statistics gpas on ref_period.refereeUserId = gpas.userId
inner join
    (select date, source, exchangeRate from fivetran_cdc_platform.currency_rate where target = 'USD') as cr on cr.date = date(gpas.createTime) and cr.source = gpas.currencycode
inner join fivetran_cdc_platform.game g on g.id = gpas.gameid
where
    (:dateFrom IS NULL OR gpas.createTime >= :dateFrom)
and
    (:dateTo IS NULL OR gpas.createTime <= :dateTo)
and
    gpas.buyineffectiveamount > 0
) gw
group by referreruserId, refereeuserid
) pts
where
    pts.points > 0
group by referreruserId) tp
inner join fivetran_cdc_platform.user u on tp.userid = u.id
where
    u.type = 'Standard'
and
    u.enabled = 1;
`;