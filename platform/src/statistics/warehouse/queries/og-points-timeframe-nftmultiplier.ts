export const OG_POINTS_TIMEFRAME_NFTMULTIPLIER = `
select
    userId as "userId",
    sweepPoints as "sweepPoints",
    crashPlinkoPoints as "crashPlinkoPoints",
    wageringPoints as "wageringPoints",
    livePoints as "livePoints",
    totalPoints as "totalPoints",
    pointsMultiplier as "pointsMultiplier",
    ROW_NUMBER() OVER (ORDER BY totalPoints DESC, UserId ASC) as "totalPointsRank"
from
(
select
    ptu.userId,
    sweep_points as sweepPoints,
    crash_plinko_points as crashPlinkoPoints,
    wagering_points as wageringPoints,
    live_points as livePoints,
    case when t.tag is not null then 2 else 1 end as pointsMultiplier,
    case
        when t.tag IS NOT NULL then (sweep_points + crash_plinko_points + wagering_points + live_points) * pointsMultiplier
        else (sweep_points + crash_plinko_points + wagering_points + live_points)
    END AS totalPoints
from
(
select pt.userId, sum(pt.sweep_points) as sweep_points, (sum(pt.crash_points) + sum(pt.plinko_points)) as crash_plinko_points, (sum(pt.wagering_points) + sum(pt.hilo_points)) as wagering_points, sum(pt.live_points) as live_points
from
(
(select ses.userId, sum(ses.count)as sweep_points, 0 as hilo_points, 0 as crash_points, 0 as plinko_points, 0 as wagering_points, 0 as live_points
from fivetran_cdc_platform.sweepstake_entry_set ses
inner join fivetran_cdc_platform.sweepstake s on ses.sweepstakeid = s.id
where
    s.presentationtype = 'Competition'
and
    ses.cost > 0
and
    ses.refundTime is NULL
and
    ses.voidTime is NULL
and
    (:datefrom IS NULL OR ses.createTime >= :datefrom)
and
    (:dateTo IS NULL OR ses.createTime <= :dateTo )
group by ses.userId)
union all
(select gp.userId, 0 as sweep_points, gp.total_usd_adjusted as hilo_points, 0 as crash_points, 0 as plinko_points, 0 as wagering_points, 0 as live_points
from (
select
    w.userId,
    sum(case when wt.purpose = 'BuyIn' then abs(wt.amountRaw) * cr.exchangeRate end) buyins_usd,
    sum(case when wt.purpose = 'Refund' then wt.amountRaw * cr.exchangeRate else 0 end) refunds_usd,
    buyins_usd - refunds_usd as total_usd,
    round(total_usd * 8) as total_usd_adjusted
from fivetran_cdc_platform.wallet_transaction wt
inner join fivetran_cdc_platform.wallet w on wt.walletid = w.id
inner join fivetran_cdc_platform.wallet_account wa on wt.accountId = wa.id
inner join
    (select date, source, exchangeRate from fivetran_cdc_platform.currency_rate where target = 'USD') as cr on cr.date = date(wt.createTime) and cr.source = wt.currencycode
where
    wt.requesterId like 'Hi-Lo%'
and
    w.userId is not null
and
    ABS(wt.amount) > 0
and
    (:datefrom IS NULL OR wt.createTime >= :datefrom)
and
    (:dateTo  IS NULL OR wt.createTime <= :dateTo )
and
    wt.currencycode NOT in ('METH','DIA','USD') -- exclude odd currencies
and
    wt.purpose IN ('BuyIn','Refund')
and
    wa.name IN ('Withdrawable', 'Custodial') -- only withdrawable / custodial
group by w.userId) gp)
union all
(select gp.userId, 0 as sweep_points, 0 as hilo_points, sum(gp.total_usd_adjusted) as crash_points, 0 as plinko_points, 0 as wagering_points, 0 as live_points
from (
select
    w.userId,
    case when wt.purpose = 'BuyIn' then abs(wt.amountRaw) * cr.exchangeRate end buyins_usd,
    case when wt.purpose = 'Refund' then wt.amountRaw * cr.exchangeRate else 0 end refunds_usd,
    buyins_usd - refunds_usd as total_usd,
    case when wt.createtime < '2024-12-23 00:00:00' then round(total_usd * 2)
    else round(total_usd * 8) end as total_usd_adjusted
from fivetran_cdc_platform.wallet_transaction wt
inner join fivetran_cdc_platform.wallet w on wt.walletid = w.id
inner join fivetran_cdc_platform.wallet_account wa on wt.accountId = wa.id
inner join
    (select date, source, exchangeRate from fivetran_cdc_platform.currency_rate where target = 'USD') as cr on cr.date = date(wt.createTime) and cr.source = wt.currencycode
where
    wt.requesterId like 'Crash%'
and
    w.userId is not null
and
    ABS(wt.amount) > 0
and
    (:datefrom IS NULL OR wt.createTime >= :datefrom)
and
    (:dateTo  IS NULL OR wt.createTime <= :dateTo )
and
    wt.currencycode NOT in ('METH','DIA','USD') -- exclude odd currencies
and
    wt.purpose IN ('BuyIn','Refund')
and
    wa.name IN ('Withdrawable', 'Custodial') -- only withdrawable / custodial
) gp
group by gp.userId)
union all
(select
    gp.userId, 0 as sweep_points, 0 as hilo_points, 0 as crash_points, 0 as plinko_points, sum(gp.total_usd_adjusted) as wagering_points, 0 as live_points
from (
select
    gpa.userId,
    case when gpa.type = 'BuyIn' then gpa.effectiveamount * cr.exchangeRate else 0 end buyins_usd,
    case when gpa.type = 'Rollback' then gpa.effectiveamount * cr.exchangeRate else 0 end rollbacks_usd,
    buyins_usd - rollbacks_usd as total_usd,
    case when gpa.createtime < '2024-12-23 00:00:00' and h.game_id is not null then round(total_usd * old_points_per_)
         when gpa.createtime >= '2024-12-23 00:00:00' and h.game_id is not null then round(total_usd * new_points_per_)
         when h.old_points_per_ is null and g.providerid = 13 then round(total_usd * 16)
         when h.old_points_per_ is null and g.typeid in (12,13,10) then round(total_usd * 2)
         when h.old_points_per_ is null and g.typeid in (8,7,14) then round(total_usd * 4)
         when h.old_points_per_ is null and g.typeid in (6,11,1,9,15) then round(total_usd * 8)
         else 0 end
         as total_usd_adjusted
from fivetran_cdc_platform.game_play_action gpa
inner join fivetran_cdc_platform.game g on gpa.gameId = g.id
left join fivetran_cdc_platform.game_historic_types h on g.id = h.game_id
inner join
    (
    select
        date,
        source,
        exchangeRate
    from
        fivetran_cdc_platform.currency_rate
    WHERE
        target = 'USD'
) AS cr ON cr.date = DATE(gpa.createTime) AND cr.source = gpa.currencyCode
where
    type in ('BuyIn', 'Rollback')
and
    gpa.gameId NOT IN (489, 512, 502, 866, 670, 872) -- exclude plinko
and
    g.typeid not in (7,11,12,13) -- exclude live
and
    (:datefrom IS NULL OR gpa.createTime >= :datefrom)
and
    (:dateTo  IS NULL OR gpa.createTime <= :dateTo )
) gp
group by gp.userId)
union all
(
select
    gp.userId, 0 as sweep_points, 0 as hilo_points, 0 as crash_points, sum(gp.total_usd_adjusted) as plinko_points, 0 as wagering_points, 0 as live_points
from (
select
    gpa.userId,
    case when gpa.type = 'BuyIn' then gpa.effectiveamount * cr.exchangeRate end buyins_usd,
    case when gpa.type = 'Rollback' then gpa.effectiveamount * cr.exchangeRate else 0 end rollbacks_usd,
    buyins_usd - rollbacks_usd as total_usd,
    case when gpa.createtime < '2024-12-23 00:00:00' and h.game_id is not null then round(total_usd * old_points_per_)
         when gpa.createtime >= '2024-12-23 00:00:00' and h.game_id is not null then round(total_usd * new_points_per_)
         when h.old_points_per_ is null and g.providerid = 13 then round(total_usd * 16)
         when h.old_points_per_ is null and g.typeid in (12,13,10) then round(total_usd * 2)
         when h.old_points_per_ is null and g.typeid in (8,7,14) then round(total_usd * 4)
         when h.old_points_per_ is null and g.typeid in (6,11,1,9,15) then round(total_usd * 8)
         else 0 end
         as total_usd_adjusted
from fivetran_cdc_platform.game_play_action gpa
inner join fivetran_cdc_platform.game g on gpa.gameId = g.id
left join fivetran_cdc_platform.game_historic_types h on g.id = h.game_id
inner join
    (
    select
        date,
        source,
        exchangeRate
    from
        fivetran_cdc_platform.currency_rate
    WHERE
        target = 'USD'
) AS cr ON cr.date = DATE(gpa.createTime) AND cr.source = gpa.currencyCode
where
    type in ('BuyIn', 'Rollback')
and
    gpa.gameId IN (489, 512, 502, 866, 670, 872) -- only plinko
and
    (:datefrom IS NULL OR gpa.createTime >= :datefrom)
and
    (:dateTo  IS NULL OR gpa.createTime <= :dateTo )
) gp
group by gp.userId)
union all
(select
    gp.userId, 0 as sweep_points, 0 as hilo_points, 0 as crash_points, 0 as plinko_points, 0 as wagering_points, sum(gp.total_usd_adjusted) as live_points
from (
select
    gpa.userId,
    case when gpa.type = 'BuyIn' then gpa.effectiveamount * cr.exchangeRate end buyins_usd,
    case when gpa.type = 'Rollback' then gpa.effectiveamount * cr.exchangeRate else 0 end rollbacks_usd,
    buyins_usd - rollbacks_usd as total_usd,
    case when gpa.createtime < '2024-12-23 00:00:00' and h.game_id is not null then round(total_usd * old_points_per_)
         when gpa.createtime >= '2024-12-23 00:00:00' and h.game_id is not null then round(total_usd * new_points_per_)
         when h.old_points_per_ is null and g.providerid = 13 then round(total_usd * 16)
         when h.old_points_per_ is null and g.typeid in (12,13,10) then round(total_usd * 2)
         when h.old_points_per_ is null and g.typeid in (8,7,14) then round(total_usd * 4)
         when h.old_points_per_ is null and g.typeid in (6,11,1,9,15) then round(total_usd * 8)
         else 0 end
         as total_usd_adjusted
from fivetran_cdc_platform.game_play_action gpa
inner join fivetran_cdc_platform.game g on gpa.gameId = g.id
left join fivetran_cdc_platform.game_historic_types h on g.id = h.game_id
inner join
    (
    select
        date,
        source,
        exchangeRate
    from
        fivetran_cdc_platform.currency_rate
    WHERE
        target = 'USD'
) AS cr ON cr.date = DATE(gpa.createTime) AND cr.source = gpa.currencyCode
where
    type in ('BuyIn', 'Rollback')
and
    g.typeid in (7,11,12,13) -- only live casino
and
    (:datefrom IS NULL OR gpa.createTime >= :datefrom)
and
    (:dateTo  IS NULL OR gpa.createTime <= :dateTo )
) gp
group by gp.userId)
) pt
group by pt.userId
) ptu
inner join fivetran_cdc_platform.user u on ptu.userId = u.id
left join fivetran_cdc_platform.user_tag t on ptu.userId = t.userId and t.tag = '$NFT:MetaWinners' and t._FIVETRAN_DELETED = 0
where
    u.type = 'Standard'
and
    u.deleted = 0
and
    u.enabled = 1
);
`;