import * as env from 'env-var';

const CONTENT_BASE_URL = env.get('CONTENT_BASE_URL').asString();

export const TOP_WINS_USER = `
WITH RankedGP AS (
    SELECT
        GP.date,
        GP.type,
        GP.id,
        GP.name,
        GP.userId,
        GP.prizeType,
        GP.prizeValue,
        GP.currencyCode,
        GP.imageUrl,
        GP.baseAmount,
        ROW_NUMBER() OVER (PARTITION BY GP.userId ORDER BY GP.baseAmount DESC) AS row_num
    FROM (
        SELECT
                *
            FROM (
            SELECT
                GP.date,
                GP.type,
                GP.id,
                GP.name,
                GP.userId,
                GP.prizeType,
                GP.prizeValue,
                GP.currencyCode,
                GP.imageUrl,
                ROUND(IFNULL(GP.baseAmount, (GP.prizeValue * CR.exchangeRate)), (SELECT decimals FROM fivetran_cdc_platform.currency where code = :baseCurrency)) as baseAmount
            FROM (
            SELECT
                GPA.createTime date,
                CASE WHEN g.typeId in (6,8) THEN 'MiniGame' ELSE 'Game' END type,
                GPA.gameId id,
                G.name name,
                GPA.userId,
                'Cash' prizeType,
                CASE WHEN GPA.providerId = 11 AND GPA.roundId = 'fdb87438-f8c1-47b9-9061-7ba052f3011b' THEN 604.4385 ELSE GPA.amount END prizeValue,
                GPA.currencyCode,
                G.thumbnail imageUrl,
                CASE WHEN GPA.providerCurrencyCode = :baseCurrency THEN GPA.providerAmount ELSE NULL END AS baseAmount
            FROM
                fivetran_cdc_platform.game_play_action GPA
            INNER JOIN
                fivetran_cdc_platform.game G on G.id = GPA.gameId
            AND
                GPA.type = 'PayOut'
            ) GP
            LEFT JOIN (
                SELECT
                    CR.date,
                    CR.source,
                    CR.exchangeRate
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
            ) AS CR ON GP.baseAmount IS NULL AND CR.date = DATE(GP.date) AND CR.source = GP.currencyCode) GPB
    ) GP
),
RankedMG AS (
    SELECT
        MGB.date,
        MGB.type,
        MGB.id,
        MGB.name,
        MGB.userId,
        MGB.prizeType,
        MGB.prizeValue,
        MGB.currencyCode,
        MGB.imageUrl,
        MGB.baseAmount,
        ROW_NUMBER() OVER (PARTITION BY MGB.userId ORDER BY MGB.baseAmount DESC) AS row_num
FROM
    (
        SELECT
            MG.*,
            ROUND((MG.prizeValue * CR.exchangeRate), (SELECT decimals FROM fivetran_cdc_platform.currency where code = :baseCurrency)) as baseAmount
        FROM (
        SELECT
            WT.createTime date,
            'MiniGame' type,
            CASE
                WHEN WT.requesterId LIKE 'Crash%' THEN 11
                ELSE 13
            END id,
            CASE
                WHEN WT.requesterId LIKE 'Crash:%' THEN 'Rekt'
                ELSE 'Rarity Hi-Lo'
            END name,
            W.userId,
            'Cash' prizeType,
            WT.amount prizeValue,
            WT.currencyCode,
            CASE
                WHEN WT.requesterId LIKE 'Crash:%' THEN '${CONTENT_BASE_URL}/minigames/rekt-180x180.png'
                ELSE '${CONTENT_BASE_URL}/minigames/killabears-hilo-2-180x180.png'
            END imageUrl
        FROM
            fivetran_cdc_platform.wallet_transaction WT
        INNER JOIN
            fivetran_cdc_platform.wallet W ON W.id = WT.walletId AND W.type = 'User'
        WHERE
            WT.purpose = 'PayOut'
        AND
            (WT.requesterid LIKE 'Crash%' OR WT.requesterid LIKE 'Hi-Lo%')) MG
        LEFT JOIN (
            SELECT
                CR.date,
                CR.source,
                CR.exchangeRate
            FROM
                fivetran_cdc_platform.currency_rate CR
            WHERE
                CR.target = :baseCurrency
        ) AS CR ON CR.date = DATE(MG.date) AND CR.source = MG.currencyCode
    ) MGB
),
RankedSG AS (
    SELECT
        SGB.date,
        SGB.type,
        SGB.id,
        SGB.name,
        SGB.userId,
        SGB.prizeType,
        SGB.prizeValue,
        SGB.currencyCode,
        SGB.imageUrl,
        SGB.baseAmount,
        ROW_NUMBER() OVER (PARTITION BY SGB.userId ORDER BY SGB.baseAmount DESC) AS row_num
FROM
    (
        SELECT *
        FROM (
            SELECT
                SG.*,
                ROUND((SG.prizeValue * CR.exchangeRate), (SELECT decimals FROM fivetran_cdc_platform.currency where code = :baseCurrency)) as baseAmount
            FROM (
            SELECT
                SPA.createTime date,
                'Sweepstake' type,
                SES.sweepstakeId id,
                COALESCE(SP.name, S.name) name,
                SES.userId,
                SP.type prizeType,
                COALESCE(SP.amount, SP.value, SP.initialValue) prizeValue,
                COALESCE(SP.currencyCode, SP.symbol, S.currencyCode) currencyCode,
                COALESCE(SP.imageUrl, SA.url, S.imageUrl) imageUrl
            FROM
                fivetran_cdc_platform.sweepstake_prize_award SPA
            INNER JOIN
                fivetran_cdc_platform.sweepstake_entry_set SES ON SES.id = SPA.entrySetId
            INNER JOIN
                fivetran_cdc_platform.sweepstake S ON S.public = 1 AND S.id = SES.sweepstakeId
            INNER JOIN
                fivetran_cdc_platform.sweepstake_prize SP ON SP.type IN ('NFT', 'StakedCash', 'StakedToken') AND SP.id = SPA.prizeId
            LEFT JOIN
                fivetran_cdc_platform.sweepstake_asset SA ON SA.sweepstakeId = S.id AND SA.type = 'Image' AND SA.key = 'hero'
            ) SG
            LEFT JOIN (
                SELECT
                    CR.date,
                    CR.source,
                    CR.exchangeRate
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
            ) AS CR ON CR.date = DATE(SG.date) AND CR.source = SG.currencyCode) SGB
    ) SGB
),
Combined AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY userId ORDER BY baseAmount DESC) AS row_num
    FROM
    (
        SELECT date, type, id, name, userId, prizeType, prizeValue, currencyCode, imageUrl, baseAmount FROM RankedGP WHERE row_num <= 20 and baseAmount >= 500
        UNION ALL
        SELECT date, type, id, name, userId, prizeType, prizeValue, currencyCode, imageUrl, baseAmount FROM RankedMG WHERE row_num <= 20 and baseAmount >= 500
        UNION ALL
        SELECT date, type, id, name, userId, prizeType, prizeValue, currencyCode, imageUrl, baseAmount FROM RankedSG WHERE row_num <= 20
    )
)
SELECT
    date as "date",
    type as "type",
    id as "id",
    name as "name",
    userId as "userId",
    prizeType as "prizeType",
    prizeValue as "prizeValue",
    currencyCode as "currencyCode",
    imageUrl as "imageUrl",
    baseAmount as "baseAmount"
FROM
    Combined
WHERE
    row_num <= 20
and
    baseAmount IS NOT NULL
ORDER BY
    userId, baseAmount ASC;
`;