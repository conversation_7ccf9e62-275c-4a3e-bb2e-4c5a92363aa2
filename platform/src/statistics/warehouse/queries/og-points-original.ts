export const OG_POINTS_TIMEFRAME_ORIGINAL = `
select
    *,
    DENSE_RANK() OVER (ORDER BY "totalPoints" DESC) as "totalPointsRank"
from
(
select
    userId as "userId",
    sweep_points as "sweepPoints",
    crash_plinko_points as "crashPlinkoPoints",
    wagering_points as "wageringPoints",
    live_points as "livePoints",
    (sweep_points + crash_plinko_points + wagering_points + live_points) as "totalPoints",
from
(
select pt.userId, sum(pt.sweep_points) as sweep_points, (sum(pt.crash_points) + sum(pt.plinko_points)) as crash_plinko_points, (sum(pt.wagering_points) + sum(pt.hilo_points)) as wagering_points, sum(pt.live_points) as live_points
from
(
(select ses.userId, sum(ses.count)as sweep_points, 0 as hilo_points, 0 as crash_points, 0 as plinko_points, 0 as wagering_points, 0 as live_points
from fivetran_cdc_platform.sweepstake_entry_set ses
inner join fivetran_cdc_platform.sweepstake s on ses.sweepstakeid = s.id
where
    s.presentationtype = 'Competition'
and
    ses.cost > 0
and
    (:dateFrom IS NULL OR ses.createTime >= :dateFrom)
and
    (:dateTo IS NULL OR ses.createTime <= :dateTo)
group by ses.userId)
union all
(select w.userId, 0 as sweep_points, ROUND((sum(abs(wt.amount) * cr.exchangeRate)) * 8) as hilo_points, 0 as crash_points, 0 as plinko_points, 0 as wagering_points, 0 as live_points
from fivetran_cdc_platform.wallet_transaction wt
inner join fivetran_cdc_platform.wallet w on wt.walletid = w.id
inner join fivetran_cdc_platform.wallet_account wa on wt.accountId = wa.id
inner join
    (select date, source, exchangeRate from fivetran_cdc_platform.currency_rate where target = 'USD') as cr on cr.date = date(wt.createTime) and cr.source = wt.currencycode
where
    wt.requesterId like 'Hi-Lo%'
and
    w.userId is not null
and
    wt.purpose = 'BuyIn'
and
    ABS(wt.amount) > 0
and
    wt.currencycode NOT in ('METH','DIA','USD') -- exclude odd currencies
and
    (:dateFrom IS NULL OR wt.createTime >= :dateFrom)
and
    (:dateTo IS NULL OR wt.createTime <= :dateTo)
and
    wa.name IN ('Withdrawable', 'Custodial') -- only withdrawable / custodial
group by w.userId)
union all
(select w.userId, 0 as sweep_points, 0 as hilo_points, ROUND((sum(abs(wt.amount) * cr.exchangeRate)) * 2) as crash_points, 0 as plinko_points, 0 as wagering_points, 0 as live_points
from fivetran_cdc_platform.wallet_transaction wt
inner join fivetran_cdc_platform.wallet w on wt.walletid = w.id
inner join fivetran_cdc_platform.wallet_account wa on wt.accountId = wa.id
inner join
    (select date, source, exchangeRate from fivetran_cdc_platform.currency_rate where target = 'USD') as cr on cr.date = date(wt.createTime) and cr.source = wt.currencycode
where
    wt.requesterId like 'Crash%'
and
    w.userId is not null
and
    wt.purpose = 'BuyIn'
and
    ABS(wt.amount) > 0
and
    (:dateFrom IS NULL OR wt.createTime >= :dateFrom)
and
    (:dateTo IS NULL OR wt.createTime <= :dateTo)
and
    wt.currencycode NOT in ('METH','DIA','USD') -- exclude odd currencies
and
    wa.name IN ('Withdrawable', 'Custodial') -- only withdrawable / custodial
group by w.userId)
union all
(select gpa.userId, 0 as sweep_points, 0 as hilo_points, 0 as crash_points, 0 as plinko_points, round(sum(gpa.effectiveamount * cr.exchangerate) * 8) as wagering_points, 0 as live_points
from fivetran_cdc_platform.game_play_action gpa
inner join fivetran_cdc_platform.game g on gpa.gameId = g.id
inner join
    (
    select
        date,
        source,
        exchangeRate
    from
        fivetran_cdc_platform.currency_rate
    WHERE
        target = 'USD'
) AS cr ON cr.date = DATE(gpa.createTime) AND cr.source = gpa.currencyCode
where
    gpa.type = 'BuyIn'
and
    gpa.gameId NOT IN (489, 512, 502, 866, 670, 872) -- exclude plinko
and (
    coalesce(h.old_type_id, g.typeId) IN (1, 6, 8)
) -- only slots, bigwheel, coinflip
and
    gpa.effectiveamount > 0
and
    (:dateFrom IS NULL OR gpa.createTime >= :dateFrom)
and
    (:dateTo IS NULL OR gpa.createTime <= :dateTo)
group by gpa.userId)
union all
(select gpa.userId, 0 as sweep_points, 0 as hilo_points, 0 as crash_points, round(sum(gpa.effectiveamount * cr.exchangerate) * 2) as plinko_points, 0 as wagering_points, 0 as live_points
from fivetran_cdc_platform.game_play_action gpa
inner join fivetran_cdc_platform.game g on gpa.gameId = g.id
inner join
    (
    select
        date,
        source,
        exchangeRate
    from
        fivetran_cdc_platform.currency_rate
    WHERE
        target = 'USD'
) AS cr ON cr.date = DATE(gpa.createTime) AND cr.source = gpa.currencyCode
where
    type = 'BuyIn'
and
    gpa.gameId IN (489, 512, 502, 866, 670, 872) -- only plinko
and
    gpa.effectiveamount > 0
and
    (:dateFrom IS NULL OR gpa.createTime >= :dateFrom)
and
    (:dateTo IS NULL OR gpa.createTime <= :dateTo)
group by gpa.userId)
union all
(select gpa.userId, 0 as sweep_points, 0 as hilo_points, 0 as crash_points, 0 as plinko_points, 0 as wagering_points, round(sum(gpa.effectiveamount * cr.exchangerate) * 4) as live_points
from fivetran_cdc_platform.game_play_action gpa
inner join fivetran_cdc_platform.game g on gpa.gameId = g.id
inner join
    (
    select
        date,
        source,
        exchangeRate
    from
        fivetran_cdc_platform.currency_rate
    WHERE
        target = 'USD'
) AS cr ON cr.date = DATE(gpa.createTime) AND cr.source = gpa.currencyCode
where
    type = 'BuyIn'
and (
    coalesce(h.old_type_id, g.typeId) = 7
) -- only live casino
and
    gpa.effectiveamount > 0
and
    (:dateFrom IS NULL OR gpa.createTime >= :dateFrom)
and
    (:dateTo IS NULL OR gpa.createTime <= :dateTo)
group by gpa.userId)
) pt
group by pt.userId
) ptu
inner join fivetran_cdc_platform.user u on ptu.userId = u.id
where
    u.type = 'Standard'
and
    u.deleted = 0
and
    u.enabled = 1
);
`;