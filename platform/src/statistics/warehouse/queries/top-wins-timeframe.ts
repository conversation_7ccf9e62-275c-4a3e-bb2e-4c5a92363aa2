import * as env from 'env-var';

const CONTENT_BASE_URL = env.get('CONTENT_BASE_URL').asString();

export const TOP_WINS_TIMEFRAME = `
WITH GameRanks AS (
    SELECT *, ROW_NUMBER() OVER (ORDER BY baseAmount DESC NULLS LAST, date DESC) AS rn
    FROM ( SELECT
                *
            FROM (
            SELECT
                GP.date,
                GP.type,
                GP.id,
                GP.name,
                GP.userId,
                GP.prizeType,
                GP.prizeValue,
                GP.currencyCode,
                GP.imageUrl,
                ROUND(IFNULL(GP.baseAmount, (GP.prizeValue * CR.exchangeRate)), (SELECT decimals FROM fivetran_cdc_platform.currency where code = :baseCurrency)) as baseAmount
            FROM (
            SELECT
                GPA.createTime date,
                'Game' type,
                GPA.gameId id,
                G.name name,
                GPA.userId,
                'Cash' prizeType,
                CASE WHEN GPA.providerId = 11 AND GPA.roundId = 'fdb87438-f8c1-47b9-9061-7ba052f3011b' THEN 604.4385 ELSE GPA.amount END prizeValue,
                GPA.currencyCode,
                G.thumbnail imageUrl,
                CASE WHEN GPA.providerCurrencyCode = :baseCurrency THEN GPA.providerAmount ELSE NULL END AS baseAmount
            FROM
                fivetran_cdc_platform.game_play_action GPA
            INNER JOIN
                fivetran_cdc_platform.game G on G.id = GPA.gameId
            WHERE
                GPA.createtime >= dateadd(dd, -:since, current_timestamp())
            AND
                GPA.type = 'PayOut'
            ) GP
            LEFT JOIN (
                SELECT
                    CR.date,
                    CR.source,
                    CR.exchangeRate
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
            ) AS CR ON GP.baseAmount IS NULL AND CR.date = DATE(GP.date) AND CR.source = GP.currencyCode) GPB
            ORDER BY
                GPB.date DESC)
),


MiniGameRanks AS (
    SELECT *, ROW_NUMBER() OVER (ORDER BY baseAmount DESC NULLS LAST, date DESC) AS rn
        FROM ( SELECT
                *
            FROM (
            SELECT
                MG.*,
                ROUND((MG.prizeValue * CR.exchangeRate), (SELECT decimals FROM fivetran_cdc_platform.currency where code = :baseCurrency)) as baseAmount
            FROM (
            SELECT
                WT.createTime date,
                'MiniGame' type,
                CASE
                    WHEN WT.requesterId LIKE 'Crash%' THEN 11
                    ELSE 13
                END id,
                CASE
                    WHEN WT.requesterId LIKE 'Crash:%' THEN 'Rekt'
                    ELSE 'Rarity Hi-Lo'
                END name,
                W.userId,
                'Cash' prizeType,
                WT.amount prizeValue,
                WT.currencyCode,
                CASE
                    WHEN WT.requesterId LIKE 'Crash:%' THEN '${CONTENT_BASE_URL}/minigames/rekt-180x180.png'
                    ELSE '${CONTENT_BASE_URL}/minigames/killabears-hilo-2-180x180.png'
                END imageUrl
            FROM
                fivetran_cdc_platform.wallet_transaction WT
            INNER JOIN
                fivetran_cdc_platform.wallet W ON W.id = WT.walletId AND W.type = 'User'
            WHERE
                WT.createTime >= dateadd(dd, -:since, current_timestamp())
            AND
                WT.purpose = 'PayOut'
            AND
                (WT.requesterid LIKE 'Crash%' OR WT.requesterid LIKE 'Hi-Lo%')) MG
            LEFT JOIN (
                SELECT
                    CR.date,
                    CR.source,
                    CR.exchangeRate
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
            ) AS CR ON CR.date = DATE(MG.date) AND CR.source = MG.currencyCode
            ) MGB
            ORDER BY
                MGB.date DESC) MGB
),


SweepstakeRanks AS (
    SELECT *, ROW_NUMBER() OVER (ORDER BY baseAmount DESC NULLS LAST, date DESC) AS rn
    FROM ( SELECT *
        FROM (
            SELECT
                SG.*,
                ROUND((SG.prizeValue * CR.exchangeRate), (SELECT decimals FROM fivetran_cdc_platform.currency where code = :baseCurrency)) as baseAmount
            FROM (
            SELECT
                SPA.createTime date,
                'Sweepstake' type,
                SES.sweepstakeId id,
                COALESCE(SP.name, S.name) name,
                SES.userId,
                SP.type prizeType,
                COALESCE(SP.amount, SP.value, SP.initialValue) prizeValue,
                COALESCE(SP.currencyCode, SP.symbol, S.currencyCode) currencyCode,
                COALESCE(SP.imageUrl, SA.url, S.imageUrl) imageUrl
            FROM
                fivetran_cdc_platform.sweepstake_prize_award SPA
            INNER JOIN
                fivetran_cdc_platform.sweepstake_entry_set SES ON SES.id = SPA.entrySetId
            INNER JOIN
                fivetran_cdc_platform.sweepstake S ON S.public = 1 AND S.id = SES.sweepstakeId
            INNER JOIN
                fivetran_cdc_platform.sweepstake_prize SP ON SP.type IN ('NFT', 'StakedCash', 'StakedToken', 'Cash', 'FundsPercentage') AND SP.id = SPA.prizeId
            LEFT JOIN
                fivetran_cdc_platform.sweepstake_asset SA ON SA.sweepstakeId = S.id AND SA.type = 'Image' AND SA.key = 'hero'
            WHERE
                SPA.createTime >= dateadd(dd, -:since, current_timestamp())) SG
            LEFT JOIN (
                SELECT
                    CR.date,
                    CR.source,
                    CR.exchangeRate
                FROM
                    fivetran_cdc_platform.currency_rate CR
                WHERE
                    CR.target = :baseCurrency
            ) AS CR ON CR.date = DATE(SG.date) AND CR.source = SG.currencyCode) SGB
            ORDER BY
                SGB.date DESC) SGB
),

TradingRanks AS (
    SELECT *, ROW_NUMBER() OVER (ORDER BY baseAmount DESC NULLS LAST, date DESC) AS rn
    FROM (
        SELECT
            O.fulfilledTime date,
            'Trade' type,
            O.id,
            TA.code name,
            O.userId,
            'Cash' prizeType,
            O.profitFundsAmount prizeValue,
            O.stakeFundsCurrencyCode currencyCode,
            CONCAT('${CONTENT_BASE_URL}/trading/tiles/', LOWER(O.assetCode), '.png') imageUrl,
            O.profitBaseAmount baseAmount
        FROM
            fivetran_cdc_platform.trading_order O
        INNER JOIN
            fivetran_cdc_platform.trading_asset TA ON TA.code = O.assetCode AND TA.public = 1
        WHERE
            O.profitBaseAmount > 500
        AND
            O.fulfilledTime >= dateadd(dd, -:since, current_timestamp())
    ) TOB
    ORDER BY
        TOB.date DESC
),

Combined AS ( SELECT *, ROW_NUMBER() OVER (ORDER BY baseAmount DESC NULLS LAST, date DESC) rc
    FROM
    (
        SELECT * FROM GameRanks WHERE rn <= :count
        UNION ALL
        SELECT * FROM MiniGameRanks WHERE rn <= :count
        UNION ALL
        SELECT * FROM SweepstakeRanks WHERE rn <= :count
        UNION ALL
        SELECT * FROM TradingRanks WHERE rn <= :count
    )
)
SELECT
    C.date as "date",
    C.type as "type",
    C.id as "id",
    C.name as "name",
    C.userId as "userId",
    C.prizeType as "prizeType",
    C.prizeValue as "prizeValue",
    C.currencyCode as "currencyCode",
    C.imageUrl as "imageUrl",
    C.baseAmount as "baseAmount"
FROM
    Combined C
INNER JOIN
    fivetran_cdc_platform.user U ON U.id = C.userId AND U.enabled = 1
LEFT JOIN
    fivetran_cdc_platform.user_tag UT ON UT.userId = C.userId AND UT.tag = 'SkipTopWins'
WHERE
    UT.tag IS NULL
ORDER BY
    C.baseAmount DESC NULLS LAST,
    C.date DESC;
`;