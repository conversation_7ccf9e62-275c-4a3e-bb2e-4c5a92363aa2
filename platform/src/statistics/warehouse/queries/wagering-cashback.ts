import { Config } from '../../../core';

const baseCurrency = Config.baseCurrency;

export const WAGERING_CASHBACK = `
SELECT
    userId as "userId",
    SUM(buyInsBase) as "wagering",
    SUM(buyInsAdjusted) as "wageringAdjusted",
    '${baseCurrency}' as "currencyCode"
FROM
    (
        SELECT
            GPA.userId,
            G.id,
            G.rtp,
            IFNULL(PARSE_JSON(G.metadata):cashbackWageringMultiplier, IFNULL(ROUND((100 - G.rtp) / 100, 2), 0.01)) wageringMultiplier,
            SUM(CASE WHEN GPA.type = 'BuyIn' THEN ROUND(GPA.effectiveAmount, 2) END) buyIns,
            SUM(IFNULL(CASE WHEN GPA.type = 'Rollback' THEN ROUND(GPA.effectiveAmount, 2) END, 0)) rollbacks,
            ROUND((buyIns - rollbacks) * CR.exchangeRate, 2) buyInsBase,
            ROUND(((buyIns - rollbacks) * wageringMultiplier) * CR.exchangeRate, 2) buyInsAdjusted,
            GPA.currencyCode
        FROM
            fivetran_cdc_platform.game_play_action GPA
        INNER JOIN
            fivetran_cdc_platform.game G ON G.id = GPA.gameId
        INNER JOIN
            fivetran_cdc_platform.currency_rate CR ON CR.source = GPA.currencyCode AND CR.target = '${baseCurrency}' AND CR.date = DATE(GPA.createTime)
        WHERE
            GPA.createTime BETWEEN :dateFrom AND :dateTo
        GROUP BY
            G.id,
            GPA.userId,
            PARSE_JSON(G.metadata),
            GPA.currencyCode,
            G.rtp,
            CR.exchangeRate
        HAVING
            buyInsBase > 0
        UNION ALL
        SELECT
            W.userId,
            G.id,
            G.rtp,
            IFNULL(PARSE_JSON(G.metadata):cashbackWageringMultiplier, IFNULL(ROUND((100 - G.rtp) / 100, 2), 0.01)) wageringMultiplier,
            CASE WHEN WT.purpose = 'BuyIn' THEN ROUND(ABS(WT.amountRaw), 2) END buyIns,
            IFNULL(CASE WHEN WT.purpose = 'Refund' THEN ROUND(WT.amountRaw, 2) END, 0) rollbacks,
            ROUND((buyIns - rollbacks) * CR.exchangeRate, 2) buyInsBase,
            ROUND(((buyIns - rollbacks) * wageringMultiplier) * CR.exchangeRate, 2) buyInsAdjusted,
            WT.currencyCode
        FROM
            fivetran_cdc_platform.wallet_transaction WT
        INNER JOIN
            fivetran_cdc_platform.wallet W ON WT.walletId = W.id AND W.type = 'User'
        INNER JOIN
            fivetran_cdc_platform.game G ON G.id = CASE WHEN WT.requesterId LIKE 'Hi-Lo:%' THEN 13 ELSE 11 END
        INNER JOIN
            fivetran_cdc_platform.currency_rate CR ON CR.source = WT.currencyCode AND CR.target = '${baseCurrency}' AND CR.date = DATE(WT.createTime)
        INNER JOIN
            fivetran_cdc_platform.wallet_account WA ON WA.id = WT.accountId AND WA.name IN ('Deposit', 'Withdrawable', 'DepositCustodial', 'Custodial')
        WHERE
            WT.purpose IN ('BuyIn', 'PayOut', 'Refund')
        AND
            WT.requesterId REGEXP '^(Hi-Lo|Crash):.*'
        AND
            WT.createTime BETWEEN :dateFrom AND :dateTo
        AND
            buyInsBase > 0
    ) D
INNER JOIN
    fivetran_cdc_platform.user U ON U.id = D.userId AND U.type = 'Standard'
WHERE
    D.buyInsAdjusted > 0
GROUP BY
    userId,
    U.displayName
ORDER BY
    "wageringAdjusted" DESC;
`;