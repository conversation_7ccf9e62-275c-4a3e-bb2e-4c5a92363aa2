export const GET_USER_TOTALS = `
SELECT
ROUND(
    D.depositsTotal,
    (
        SELECT
            decimals
        FROM
            fivetran_cdc_platform.currency
        WHERE
            code = :baseCurrency
    )
) AS "depositsTotal",
ROUND(
    WDT.withdrawalsTotal,
    (
        SELECT
            decimals
        FROM
            fivetran_cdc_platform.currency
        WHERE
            code = :baseCurrency
    )
) AS "withdrawalsTotal",
ROUND(
    GPA.wageringTotal,
    (
        SELECT
            decimals
        FROM
            fivetran_cdc_platform.currency
        WHERE
            code = :baseCurrency
    )
) AS "wageringTotal",
ROUND(
    GPA.gGRTotal,
    (
        SELECT
            decimals
        FROM
            fivetran_cdc_platform.currency
        WHERE
            code = :baseCurrency
    )
) AS "gGRTotal",
ROUND(
    GPA.nGRTotal,
    (
        SELECT
            decimals
        FROM
            fivetran_cdc_platform.currency
        WHERE
            code = :baseCurrency
    )
) AS "nGRTotal",
ROUND(
    GPA.effectiveWageringTotal,
    (
        SELECT
            decimals
        FROM
            fivetran_cdc_platform.currency
        WHERE
            code = :baseCurrency
    )
) AS "effectiveWageringTotal",
ROUND(
    GPA.payOutTotal,
    (
        SELECT
            decimals
        FROM
            fivetran_cdc_platform.currency
        WHERE
            code = :baseCurrency
    )
) AS "payOutTotal",
FROM
(
    SELECT
        IFNULL (SUM(_D.amount * RATES.exchangeRate), 0) AS depositsTotal
    FROM
        (
            SELECT
                payment.id,
                payment.amount,
                payment.currencyCode,
                payment.createTime
            FROM
                fivetran_cdc_platform.payment
            WHERE
                payment.userId = :userId
                AND payment.status = 'Successful'
                AND payment.type = 'Deposit'
                AND (:dateFrom IS NULL OR :dateFrom = '' OR payment.createTime BETWEEN :dateFrom AND :dateTo)
        ) AS _D
        LEFT JOIN (
            SELECT
                cr.date AS currentDate,
                cr.exchangeRate,
                cr.source,
                cr.target
            FROM
                fivetran_cdc_platform.currency_rate cr
            WHERE
                cr.target = :baseCurrency
        ) AS RATES ON _D.currencyCode = RATES.source AND DATE(_D.createTime) = RATES.currentDate
) AS D,
(
    SELECT
        IFNULL (SUM(_WDT.amount * RATES.exchangeRate), 0) AS withdrawalsTotal
    FROM
        (
            SELECT
                withdrawal.id,
                withdrawal.amount,
                withdrawal.currencyCode,
                withdrawal.createTime
            FROM
                fivetran_cdc_platform.withdrawal
            WHERE
                withdrawal.userId = :userId
                AND withdrawal.status = 'Complete'
                AND (:dateFrom IS NULL OR :dateFrom = '' OR withdrawal.createTime BETWEEN :dateFrom AND :dateTo)
        ) AS _WDT
        LEFT JOIN (
            SELECT
                cr.date AS currentDate,
                cr.exchangeRate,
                cr.source,
                cr.target
            FROM
                fivetran_cdc_platform.currency_rate cr
            WHERE
                cr.target = :baseCurrency
        ) AS RATES ON _WDT.currencyCode = RATES.source AND DATE(_WDT.createTime) = RATES.currentDate
) AS WDT,
(
    SELECT
        IFNULL (SUM(CASE WHEN type = 'BuyIn' THEN CAST(effectiveAmount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END) -
        SUM(CASE WHEN type = 'Rollback' THEN CAST(effectiveAmount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END), 0) AS effectiveWageringTotal,

        IFNULL (SUM(CASE WHEN type = 'BuyIn' THEN CAST(amount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END) -
        SUM(CASE WHEN type = 'Rollback' THEN CAST(amount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END), 0) AS wageringTotal,

        IFNULL (wageringTotal - SUM(CASE WHEN type = 'PayOut' THEN CAST(amount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END), 0) AS gGRTotal,

        IFNULL (SUM(CASE WHEN type = 'PayOut' THEN CAST(amount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END), 0) AS payOutTotal,

        IFNULL (SUM(CASE WHEN type = 'BuyIn' THEN CAST(effectiveAmount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END) -
        SUM(CASE WHEN type = 'Rollback' THEN CAST(effectiveAmount * RATES.exchangeRate AS NUMBER(38,0)) ELSE 0 END) - payOutTotal, 0) AS nGRTotal
    FROM
    (
        SELECT
            game_play_action.type,
            game_play_action.amount,
            game_play_action.effectiveAmount,
            game_play_action.currencyCode,
            game_play_action.createTime
        FROM
            fivetran_cdc_platform.game_play_action
        WHERE
            userId = :userId
            AND (:dateFrom IS NULL OR :dateFrom = '' OR game_play_action.createTime BETWEEN :dateFrom AND :dateTo)
    ) AS _GPA
    LEFT JOIN (
        SELECT
            cr.date AS currentDate,
            cr.exchangeRate,
            cr.source,
            cr.target
        FROM
            fivetran_cdc_platform.currency_rate cr
        WHERE
            cr.target = :baseCurrency
    ) AS RATES ON _GPA.currencyCode = RATES.source AND DATE(_GPA.createTime) = RATES.currentDate
) AS GPA;
`;