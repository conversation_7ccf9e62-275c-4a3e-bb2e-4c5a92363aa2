import { Inject, Singleton } from '../../../core/ioc';
import { LogClass } from '../../../core/logging';
import { Config, JsonSerialiser, ParameterStore } from '../../../core';
import { UnauthorizedError } from '../../../core';
import { ApiRequest } from '../../../api';
import { OneBitTechAuthRequest } from '../interfaces/request';
import crypto from 'crypto';

@Singleton
@LogClass()
export class OneBitTechHashVerifier {
    constructor(
        @Inject private readonly jsonSerialiser: JsonSerialiser,
        @Inject private readonly parameterStore: ParameterStore) {
    }

    public async verify(authRequest: OneBitTechAuthRequest, apiRequest: ApiRequest): Promise<void> {
        const requestHash = apiRequest.headers[`hash`];
        const hash = await this.generateHash(authRequest);

        if (hash !== requestHash)
            throw new UnauthorizedError('Authentication token is invalid.');
    }

    private async generateHash(request: OneBitTechAuthRequest): Promise<string> {
        const secretKey = await this.parameterStore.get(`/${Config.stage}/integration/onebittech/secret`, true, true);
        const body = this.jsonSerialiser.serialise(request);
        return crypto
            .createHmac('sha256', secretKey)
            .update(body)
            .digest('base64');
    }
}