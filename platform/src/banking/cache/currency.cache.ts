import { <PERSON>ton, Inject } from '../../core/ioc';
import { <PERSON><PERSON>, JsonSerialiser } from '../../core';
import { CacheKeyGenerator } from '../../core/cache';
import { Currency } from '../currency';
import { LogClass } from '../../core/logging';

@Singleton
@LogClass()
export class CurrencyCache {
    private readonly cacheKeyGenerator = new CacheKeyGenerator('BANKING:Currencies');

    constructor(
        @Inject private readonly redis: Redis,
        @Inject private readonly serialiser: JsonSerialiser) {
    }

    public async getAll(): Promise<Currency[]> {
        const cacheKey = this.cacheKeyGenerator.generate();
        const rawItems = await this.redis.cluster.hvals(cacheKey) as string[];

        if (!rawItems || rawItems.length === 0)
            return [];

        return rawItems.map(r => this.deserialise(r));
    }

    public async getMany(...codes: string[]): Promise<Currency[]> {
        const cacheKey = this.cacheKeyGenerator.generate();
        const rawItems = await this.redis.cluster.hmget(cacheKey, ...codes.map(c => c.toUpperCase())) as string[];

        if (!rawItems || rawItems.length === 0)
            return [];

        return rawItems.map(r => this.deserialise(r)).filter(c => c);
    }

    public async get(code: string): Promise<Currency | undefined> {
        const cacheKey = this.cacheKeyGenerator.generate();
        const rawItem = await this.redis.cluster.hget(cacheKey, code.toUpperCase());

        if (!rawItem)
            return undefined;

        return this.deserialise(rawItem);
    }

    public async store(...currencies: Currency[]): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate();

        for (const currency of currencies)
            await this.redis.cluster.hset(cacheKey, currency.code.toUpperCase(), this.serialiser.serialise(currency));
    }

    public async remove(code: string): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate();
        await this.redis.cluster.hdel(cacheKey, code.toUpperCase());
    }

    public async clear(): Promise<void> {
        const cacheKey = this.cacheKeyGenerator.generate();
        await this.redis.cluster.del(cacheKey);
    }

    private deserialise(raw: string): Currency {
        const currency = this.serialiser.deserialise<Currency>(raw);

        if (currency) {
            // Backwards compatibility
            if (!currency.supportedContexts)
                currency.supportedContexts = [];

            if (currency.visible === undefined)
                currency.visible = true;

            if (currency.factor === undefined)
                currency.factor = 1;

            if (currency.public === undefined)
                currency.public = true;

            if (currency.playable === undefined)
                currency.playable = true;

            if (currency.redeemable === undefined)
                currency.redeemable = false;
        }

        return currency;
    }
}