import { Inject, Singleton } from '../../core/ioc';
import { LogClass } from '../../core/logging';
import { BadRequestError, BigNumber, NotFoundError, RequesterType } from '../../core';
import { CurrencyConverter, CurrencyManager, Ledger, PlatformWallets, TransactionPurpose, WalletAccountManager } from '../../banking';
import { TransferResult } from '../../banking/transfer';
import { SportbookTransactionProcessorBase, TransactionProcessor } from './sportbook-transaction.processors';
import { RollbackTransaction } from './sportbook-action';
import { RollbackTransactionOutcome } from './sportbook-action-outcome';
import { SportbookTransactionManager } from '../sportbook-transaction-manager';
import { SportbookTransactionType } from '../sportbook-transaction-type';
import { SportbookBetslipManager } from '../sportbook-betslip-manager';
import { SportbookProvider } from '../sportbook-provider';
import { SportbookUserWalletAccountResolver } from '../sportbook-user-wallet-account-resolver';
import { SportbookBetSlipStatus } from '../sportbook-betslip-status';
import { SportbookSessionManager } from '../sportbook-session-manager';
import { SportbookSession } from '../sportbook-session';
import { getEffectiveAmount } from '../../banking/utilities';
import { SportbookBalanceRetriever } from '../sportbook-balance-retriever';
import { SportbookBetSlipOutcome } from '../sportbook-betslip-outcome';
import { SportbookTransaction } from '../sportbook-transaction';
import moment from 'moment';

@Singleton
@LogClass()
export class SportbookRollbackProcessor extends SportbookTransactionProcessorBase implements TransactionProcessor<RollbackTransaction, RollbackTransactionOutcome> {
    constructor(
        @Inject protected readonly walletManager: WalletAccountManager,
        @Inject protected readonly accountResolver: SportbookUserWalletAccountResolver,
        @Inject protected readonly sessionManager: SportbookSessionManager,
        @Inject protected readonly betslipManager: SportbookBetslipManager,
        @Inject protected readonly transactionManager: SportbookTransactionManager,
        @Inject protected readonly currencyManager: CurrencyManager,
        @Inject protected readonly currencyConverter: CurrencyConverter,
        @Inject protected readonly balanceRetriever: SportbookBalanceRetriever,
        @Inject private readonly ledger: Ledger) {
        super(walletManager, accountResolver, sessionManager, betslipManager, transactionManager, currencyManager, currencyConverter, balanceRetriever);
    }

    public async process(rollback: RollbackTransaction): Promise<RollbackTransactionOutcome> {
        if (!rollback.providerRef)
            throw new BadRequestError('Rollback provider reference is required.');

        const parentTransaction = await this.getTransactionByProviderRef(rollback.provider, rollback.parentTransactionId);

        if (!parentTransaction)
            throw new BadRequestError(`Parent transaction ${rollback.parentTransactionId} not found.`);

        const betslip = await this.getBetslip(parentTransaction.betSlipId);

        if (!betslip)
            throw new NotFoundError(`Betslip for provider ${SportbookProvider[rollback.provider]} with ref ${rollback.parentTransactionId} not found.`);

        if (betslip.status !== SportbookBetSlipStatus.Settled)
            throw new BadRequestError(`Betslip ${parentTransaction.betSlipId} has not already been settled. Rollback ref ${rollback.providerRef}`);

        if (betslip.outcome !== rollback.rollbackType)
            throw new BadRequestError(`Betslip ${parentTransaction.betSlipId} outcome is not ${rollback.rollbackType}. Rollback ref ${rollback.providerRef}`);

        const { allowed, reason } = await this.rollbackAllowed(parentTransaction);

        if (!allowed)
            throw new BadRequestError(`Rollback is not allowed for transaction ${parentTransaction.id}. Reason: ${reason}`);

        const session = await this.getSession(betslip.sessionId);
        const existingTransaction = betslip.transactions.find(t => t.providerRef === rollback.providerRef);

        if (existingTransaction)
            return {
                id: existingTransaction.id,
                type: SportbookTransactionType.Rollback,
                providerRef: existingTransaction.providerRef,
                userId: existingTransaction.userId,
                balance: existingTransaction.providerBalance,
                amount: existingTransaction.providerAmount,
                currencyCode: existingTransaction.currencyCode,
                providerCurrencyCode: existingTransaction.providerCurrencyCode,
                parentTransactionId: parentTransaction.id.toString()
            };

        let result: TransferResult | undefined;
        const rollbackConversion = await this.convertAmount(rollback.providerCurrencyCode, session.currency, rollback.amount);
        const sources = await this.accountResolver.getForBuyIn(session);

        const { amount, balanceDifference } = await this.fundsAvailability(session, parentTransaction.rate, parentTransaction.amount);
        // Just logging this for now. It will depends how we want to handle rollbacks on wins
        console.log('BALANCE DIFFERENCE', balanceDifference);

        // If loserollback amount will be zero. No ledger entry required but transaction recorded
        // If winrollback amount > zero take funds from user wallet, currency comes from the parent transaction
        if (rollback.amount.isGreaterThan(0))
            result = await this.ledger
                .transfer(amount, parentTransaction.currencyCode)
                .purpose(TransactionPurpose.BuyIn)
                .requestedBy(RequesterType.System, `${SportbookProvider[rollback.provider]}:${rollback.providerRef}:${parentTransaction.id}`)
                .externalRef(rollback.providerRef)
                .memo(`Rollback for ${SportbookProvider[rollback.provider]}: parent transaction id: ${parentTransaction.id}`)
                .fromUser(rollback.userId, ...sources)
                .toPlatform(PlatformWallets.Corporate)
                .commit();

        const { amount: balance, provider: providerBalance } = await this.getBalance(session.tokenId, rollbackConversion.rate);
        const effectiveAmount = getEffectiveAmount(result);

        const rollbackTransaction = await this.transactionManager.add({
            type: SportbookTransactionType.Rollback,
            provider: rollback.provider,
            providerRef: rollback.providerRef,
            userId: rollback.userId,
            betSlipId: parentTransaction.betSlipId,
            providerCurrencyCode: parentTransaction.providerCurrencyCode,
            providerAmount: parentTransaction.providerAmount,
            amount,
            effectiveAmount,
            currencyCode: parentTransaction.currencyCode,
            balance: balance.total,
            rate: rollbackConversion.rate,
            providerBalance: providerBalance.total,
            parentTransactionId: parentTransaction.id,
            walletEntryId: result?.entry.id,
            fundsContext: parentTransaction.fundsContext,
            bonusId: rollback.bonusId
        });

        // We need to reopen the bet slip by setting the status to Accepted
        // or any following transaction will be rejected due to the betslip being in a settled state
        // status from Settled to Accepted and outcome from Won/Lost to Open
        await this.betslipManager.update(rollbackTransaction, {
            status: SportbookBetSlipStatus.Accepted,
            outcome: SportbookBetSlipOutcome.Open
        });

        // await this.eventDispatcher.send(new SportbookRollbackEvent({
        //     userId: parentTransaction.userId,
        //     betSlipId: parentTransaction.betSlipId,
        //     provider: parentTransaction.provider,
        //     parentTransactionId: parentTransaction.id,
        //     parentTransactionType: parentTransaction.type,
        //     providerAmount: rollback.amount,
        //     amount,
        //     effectiveAmount,
        //     balance: balance.total,
        //     providerBalance: providerBalance.total,
        //     balanceDifference,
        //     providerCurrency: parentTransaction.providerCurrencyCode,
        //     currencyCode: session.currency,
        //     fundsContext: parentTransaction.fundsContext,
        //     walletEntryId: result?.entry.id,
        //     date: new Date()
        // }));

        return {
            id: rollbackTransaction.id,
            type: SportbookTransactionType.Rollback,
            providerRef: rollbackTransaction.providerRef,
            userId: rollbackTransaction.userId,
            balance: rollbackTransaction.providerBalance,
            amount: rollbackTransaction.providerAmount,
            currencyCode: rollbackTransaction.currencyCode,
            providerCurrencyCode: rollbackTransaction.providerCurrencyCode,
            parentTransactionId: parentTransaction.id.toString(),
        };
    }

    // TODO needs to be reviewed if we decide to use the available balance and not negative balance
    private async fundsAvailability(session: SportbookSession, rate: BigNumber, rollbackAmount: BigNumber): Promise<{ amount: BigNumber, balanceDifference: BigNumber; }> {
        const { amount: balance } = await this.getBalance(session.tokenId, rate);

        const amount = rollbackAmount.isGreaterThan(balance.total)
            ? balance.total
            : rollbackAmount;

        const result = {
            amount,
            balanceDifference: balance.total.minus(rollbackAmount)
        };

        return result;
    }

    private async rollbackAllowed(parentTransaction: SportbookTransaction): Promise<{ allowed: boolean; reason?: string; }> {
        const windowCloseTime = moment.utc(parentTransaction.createTime).add(30, 'days');
        console.log('WINDO')
        const now = moment.utc();

        if (now.isAfter(windowCloseTime)) {
            const totalMinutes = now.diff(windowCloseTime, 'minutes');
            const days = Math.floor(totalMinutes / (24 * 60));
            const hours = Math.floor((totalMinutes % (24 * 60)) / 60);
            const minutes = totalMinutes % 60;

            const timeParts: string[] = [];
            if (days > 0) timeParts.push(`${days} day${days > 1 ? 's' : ''}`);
            if (hours > 0) timeParts.push(`${hours} hour${hours > 1 ? 's' : ''}`);
            if (minutes > 0) timeParts.push(`${minutes} minute${minutes > 1 ? 's' : ''}`);

            const timeAgoText = timeParts.join(', ');

            return {
                allowed: false,
                reason: `Rollback window closed ${timeAgoText} ago.`
            };
        }

        return { allowed: true };
    }
}