import { Body, ClientController, Delete, FeatureEnabled, Get, Path, Post, Put, Query, Response, Route, Security, SuccessResponse, Tags, Throttle } from '@tcom/platform/lib/api';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { LogClass, UserLog } from '@tcom/platform/lib/core/logging';
import {
    RewardEligibilityDeterminer,
    RewardFilter,
    RewardManager,
    RewardStatus,
    RewardTemplate,
    RewardTemplateManager,
    RewardTemplateRedemptionType,
    RewardTemplateSourceType,
    RewardTemplateStatusType,
    REWARD_TEMPLATE_IDENTIFIER_REGEX,
    RewardAppliedCodeManager,
    RewardPendingFilter,
    RewardClaimedTotals,
    RewardEligibilityDeterminerResult
} from '@tcom/platform/lib/reward';
import { RewardTemplateClaimMaximumError, RewardTemplateClaimEligibilityError, RewardTemplateClaimError, RewardTemplateClaimExpiredError, REWARD_TEMPLATE_CLAIM_ERROR_CODE } from '@tcom/platform/lib/reward/errors';
import { RewardAwarderFactory } from '@tcom/platform/lib/reward/awards';
import { BadRequestError, ForbiddenError, NotFoundError, PagedResult, UnauthorizedError } from '@tcom/platform/lib/core';
import { checkUnsatisfiedContributors, checkUnsatisfiedRequirements } from '@tcom/platform/lib/reward/utilities';
import { RewardModelMapper, RewardSummaryModelMapper } from '@tcom/platform/lib/reward/models/mappers';
import { RewardModel, RewardSummaryModel } from '@tcom/platform/lib/reward/models';
import { RewardBonusModel } from '@tcom/platform/lib/reward/balances/models';
import { RewardBonusMapper } from '@tcom/platform/lib/reward/balances/models/mappers';
import { ApplyCodeModel, ClaimRewardModel } from '../models';
import moment from 'moment';
import _ from 'lodash';

@Tags('Rewards')
@Route('reward')
@Security('cognito')
@LogClass()
export class RewardController extends ClientController {
    constructor(
        @Inject private readonly bonusMapper: RewardBonusMapper,
        @Inject private readonly rewardManager: RewardManager,
        @Inject private readonly rewardTemplateManager: RewardTemplateManager,
        @Inject private readonly rewardEligibilityDeterminer: RewardEligibilityDeterminer,
        @Inject private readonly rewardAwarderFactory: RewardAwarderFactory,
        @Inject private readonly rewardMapper: RewardModelMapper,
        @Inject private readonly rewardSummaryMapper: RewardSummaryModelMapper,
        @Inject private readonly rewardAppliedCodeManager: RewardAppliedCodeManager,
        @Inject private readonly userLog: UserLog) {
        super();
    }

    /**
     * @summary Gets a list of users rewards
     */
    @Get()
    public async getAll(
        @Query() redemptionType?: RewardTemplateRedemptionType,
        @Query() status?: RewardStatus[],
        @Query() templateIdentifier?: string,
        @Query() page: number = 1,
        @Query() pageSize: number = 20
    ): Promise<PagedResult<RewardModel>> {
        const filter: RewardFilter = {
            userId: this.user.id,
            status: _.remove(status || [], RewardStatus.Failed),
            redemptionType,
            templateIdentifier,
            page,
            pageSize,
            order: {
                status: 'DESC'
            }
        };

        const rewards = await this.rewardManager.getAll(filter);
        const models: RewardModel[] = [];

        for (const r of rewards.items)
            models.push(this.rewardMapper.map(r));

        return new PagedResult(models, rewards.totalCount, page, pageSize);
    }

    @Get('claimed')
    @FeatureEnabled('reward-balance-allocation')
    public async getTotalClaimed(): Promise<RewardClaimedTotals> {
        return this.rewardManager.getTotalClaimedForUser(this.user.id);
    }

    /**
     * @summary Gets all unclaimed and non-expired rewards for the listed RewardTemplateSourceType
     */
    @Get('unclaimed')
    @FeatureEnabled('reward-balance-allocation')
    public async getAllUnclaimed(): Promise<RewardBonusModel> {
        const rewards = await this.rewardManager.getUnclaimedRewards(this.user.id, [
            RewardTemplateSourceType.WinBack,
            RewardTemplateSourceType.DailyReward,
            RewardTemplateSourceType.WeeklyReward,
            RewardTemplateSourceType.MonthlyReward,
            RewardTemplateSourceType.RankUpReward,
            RewardTemplateSourceType.Cashback
        ]);

        return this.bonusMapper.map(this.user.id, rewards);
    }

    /**
     * @summary Gets a list of users pending rewards
     */
    @Get('pending')
    public async getPending(@Query() templateIdentifier?: string): Promise<RewardModel[]> {
        const filter: RewardPendingFilter = {
            userId: this.user.id,
            cacheOnly: true,
            templateIdentifier
        };

        const rewards = await this.rewardManager.getPending(filter);

        return rewards.map(r => this.rewardMapper.map(r));
    }

    /**
     * @summary Gets a list of users recent rewards
     */
    @Get('recent')
    public async getRecent(
        @Query() templateIdentifier?: string,
        @Query() status: RewardStatus[] = Object.values(RewardStatus),
        @Query() limit: number = 3
    ): Promise<RewardModel[]> {
        if (limit > 3)
            throw new BadRequestError(`Limit cannot exceed 3.`);

        const rewards = await this.rewardManager.getRecent({
            userId: this.user.id,
            templateIdentifier,
            status,
            limit
        });

        return rewards.map(r => this.rewardMapper.map(r));
    }

    /**
     * @summary Gets a reward for the authenticated user
     */
    @Get('{id}')
    public async get(@Path() id: number): Promise<RewardModel> {
        const reward = await this.rewardManager.get(id);

        if (!reward)
            throw new NotFoundError(`Reward not found.`);

        if (reward.user.id !== this.user.id)
            throw new NotFoundError(`Reward not found.`);

        return this.rewardMapper.map(reward);
    }

    /**
     * @summary Gets a list of users reward summaries
     */
    @Get('summaries')
    public async getSummaries(): Promise<RewardSummaryModel[]> {
        const rewards = await this.rewardManager.getAllSummariesForUser(this.user.id);
        const models: RewardSummaryModel[] = [];

        for (const r of rewards)
            models.push(await this.rewardSummaryMapper.map(r));

        return models;
    }

    /**
     * @summary Gets the applied reward code for the authenticated user
     */
    @Get('code/{source}')
    @SuccessResponse(200, 'Ok')
    public async getActiveCode(@Path() source: RewardTemplateSourceType): Promise<string> {
        const code = await this.rewardAppliedCodeManager.get(this.user.id, source);

        if (!code)
            throw new NotFoundError(`Code not found.`);

        return code;
    }

    /**
     * @summary Applies a reward code for the authenticated user
     */
    @Post('code')
    @Throttle()
    @SuccessResponse(200, 'Ok')
    @Response<UnauthorizedError>(401)
    public async applyCode(@Body() model: ApplyCodeModel): Promise<void> {
        if (!model.code)
            throw new BadRequestError(`Code not supplied.`);

        await this.rewardAppliedCodeManager.apply(this.user.id, model.source, model.code);
    }

    /**
     * @summary Removes a reward code for the authenticated user
     */
    @Delete('code/{source}')
    @Throttle()
    @SuccessResponse(200, 'Ok')
    @Response<UnauthorizedError>(401)
    public async removeActiveCode(@Path() source: RewardTemplateSourceType): Promise<void> {
        await this.rewardAppliedCodeManager.remove(this.user.id, source);
    }

    /**
     * @summary Checks user eligibility for a reward template by code
     */
    @Post('eligibility')
    @Throttle()
    @SuccessResponse(200, 'Ok')
    @Response<UnauthorizedError>(401)
    public async checkEligibility(@Body() model: ClaimRewardModel): Promise<RewardEligibilityDeterminerResult> {
        const template = await this.validateAndGetTemplate(model.code);
        return this.rewardEligibilityDeterminer.determine(this.user.id, template);
    }

    /**
     * @summary Claims a reward by identifier for the authenticated user
     */
    @Post('claim')
    @Throttle({ requestCount: 2, rateSeconds: 1 })
    @SuccessResponse(200, 'Ok')
    @Response<UnauthorizedError>(401)
    public async claim(@Body() model: ClaimRewardModel): Promise<RewardModel> {
        const template = await this.validateAndGetTemplate(model.code);

        const result = await this.rewardEligibilityDeterminer.determine(this.user.id, template);

        if (!result.passed)
            throw new RewardTemplateClaimEligibilityError(result.reason || 'User not eligible for reward.', result.eligibility, result.code, result.metadata);

        const awarder = this.rewardAwarderFactory.create(template.award.type);
        const newReward = await awarder.create(this.user.id, template.award, template.source.type);
        const created = await this.rewardManager.add(newReward);

        if (checkUnsatisfiedRequirements(created) || checkUnsatisfiedContributors(created) || (created.expireTime && created.awardOnExpiry))
            return this.rewardMapper.map(created);

        const awarded = await awarder.award(created);
        return this.rewardMapper.map(awarded);
    }

    /**
     * @summary Cancels a pending reward for the authenticated user
     */
    @Put('{id}/cancel')
    @SuccessResponse(200, 'Ok')
    @Response<UnauthorizedError>(401)
    public async cancel(@Path() id: number): Promise<RewardModel> {
        const reward = await this.rewardManager.get(id);

        if (!reward)
            throw new NotFoundError(`Reward not found.`);

        if (reward.user.id !== this.user.id)
            throw new NotFoundError(`Reward not found.`);

        if (reward.status !== RewardStatus.Pending)
            throw new ForbiddenError(`Reward cannot be cancelled.`);

        return this.userLog.handle(this.user.id, 'Reward:Cancel', async (logData) => {
            logData.rewardId = id;
            const updated = await this.rewardManager.setStatus(reward.id, RewardStatus.Cancelled);
            return this.rewardMapper.map(updated);
        });
    }

    /**
     * @summary Validates and retrieves a reward template by code
     */
    private async validateAndGetTemplate(code: string): Promise<RewardTemplate> {
        if (!code)
            throw new BadRequestError(`Code not supplied.`);

        code = code.toUpperCase();

        if (!REWARD_TEMPLATE_IDENTIFIER_REGEX.test(code))
            throw new BadRequestError(`Invalid code supplied.`);

        const template = await this.rewardTemplateManager.getByIdentifier(code);

        if (!template)
            throw new NotFoundError(`Reward template not found.`);

        if (template.source.type !== RewardTemplateSourceType.Code)
            throw new NotFoundError(`Reward template not found.`);

        if (template.redemption !== RewardTemplateRedemptionType.Claimable)
            throw new NotFoundError(`Reward template not found.`);

        const now = moment();

        if (template.startTime && now.isBefore(template.startTime))
            throw new RewardTemplateClaimError(`Reward template not active.`, REWARD_TEMPLATE_CLAIM_ERROR_CODE.Inactive);

        if (template.expireTime && now.isSameOrAfter(template.expireTime))
            throw new RewardTemplateClaimExpiredError(`Reward template expired.`, template.expireTime);

        if (template.status !== RewardTemplateStatusType.Active)
            throw new RewardTemplateClaimError(`Reward template not active.`, REWARD_TEMPLATE_CLAIM_ERROR_CODE.Inactive);

        if (template.maximumClaims) {
            const count = await this.rewardManager.getCountByTemplateId(template.id);

            if (count >= template.maximumClaims)
                throw new RewardTemplateClaimMaximumError('Reward template claimed more times than configured threshold.', template.maximumClaims);
        }

        return template;
    }
}