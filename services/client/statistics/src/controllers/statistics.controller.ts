import { Get, Response, Route, Tags, ClientController, Security, Path, Query } from '@tcom/platform/lib/api';
import { UnauthorizedError } from '@tcom/platform/lib/core/errors';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { StatisticsManager, StatisticsReferralsDepositsByUser, StatisticsUserOGPoints, StatisticsVIPLossbackWageringSummary, StatisticsWageringSummary } from '@tcom/platform/lib/statistics';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { SweepstakeTotelsModel } from '../models/sweepstake-totals.model';
import { OGPointsLeaderboardModelMapper, StatisticsUserOGPointsModelMapper } from '../models/mappers';
import { LeaderboardModel } from '@tcom/platform/lib/leaderboard/models';
import { StatisticsVIPSummaryModel } from '../models';
import _ from 'lodash';
import { BigNumber } from '@tcom/platform/lib/core';
import moment from 'moment';
import { UserWalletAccounts, WalletAccountManager } from '@tcom/platform/lib/banking';

@Tags('Statistics')
@Route('statistics')
@Security('cognito', ['anonymous'])
@LogClass()
export class StatisticsController extends ClientController {
    constructor(
        @Inject private readonly statsManager: StatisticsManager,
        @Inject private readonly userOGPointsMapper: StatisticsUserOGPointsModelMapper,
        @Inject private readonly leaderboardMapper: OGPointsLeaderboardModelMapper,
        @Inject private readonly walletAccountManager: WalletAccountManager) {
        super();
    }

    /**
     * @summary Get referrer deposit statistics
     */
    @Get('referral/deposit')
    @Response<UnauthorizedError>(401)
    public async getReferralDepositByUser(): Promise<StatisticsReferralsDepositsByUser> {
        return this.statsManager.getReferralDepositByUser(this.user.id);
    }

    /**
     * @summary Get totals
     */
    @Get('total')
    @Response<UnauthorizedError>(401)
    public async getTotals(): Promise<SweepstakeTotelsModel> {
        const data = await this.statsManager.getTotals();

        return {
            totalSignUps: data.totalSignUps,
            sweepstakesActive: data.sweepstakesActive,
            sweepstakesEntries: data.sweepstakesEntries,
            sweepstakesWinners: data.sweepstakesWinners,
            sweepstakesCompleted: data.sweepstakesCompleted,
            sweepstakesNFTPrizeValue: data.sweepstakesPrizeValue,
            createTime: data.createTime
        };
    }

    @Get('ogpoints')
    @Security('cognito')
    @Response<UnauthorizedError>(401)
    public async getOGPointsByUser(): Promise<StatisticsUserOGPoints> {
        const userId = this.user.id;
        let points = await this.statsManager.getOGPointsByUser(this.user.id);
        const casinoPoints = await this.walletAccountManager.getForUser(userId, UserWalletAccounts.Withdrawable, 'CASINO');

        const additionalPointsMap: { [key: string]: number } = {
            casino: casinoPoints?.balance?.toNumber() || 0
        };

        if (points || additionalPointsMap.casino > 0) {
            points = points || { userId, detail: {}, lastUpdated: new Date() };
            return this.userOGPointsMapper.map(points, additionalPointsMap);
        }

        return {
            userId,
            detail: {},
            lastUpdated: new Date()
        };

    }

    @Get('ogpoints/leaderboard/{identifier}')
    @Response<UnauthorizedError>(401)
    public async getOGPointsLeaderboard(@Path() identifier: string, @Query() skip: number = 0, @Query() take: number = 50): Promise<LeaderboardModel> {
        take = Math.min(take, 100);
        const [rankings, total] = await this.statsManager.getOGPointsRankings(identifier, skip, take);
        return this.leaderboardMapper.map(identifier, rankings, total, this.user?.id);
    }

    @Get('vip/award/summary')
    @Security('cognito')
    @Response<UnauthorizedError>(401)
    public async getVIPAwardSummary(): Promise<StatisticsVIPSummaryModel> {
        if (this.user.level < 1)
            throw new UnauthorizedError(`You must be a VIP to access this data.`);

        const result = await this.statsManager.getVIPAwardSummary(this.user.id);

        return {
            detail: result,
            totalCount: _.sumBy(result, r => r.count),
            totalAmountBase: BigNumber.sum(...result.map(r => r.amountBase)).decimalPlaces(2)
        };
    }

    @Get('wagering/summary')
    @Security('cognito')
    @Response<UnauthorizedError>(401)
    public async getWageringSummary(): Promise<StatisticsWageringSummary> {
        const from = moment().utc().add(-30, 'days').toDate();

        return this.statsManager.getWageringSummary(this.user.id, from);
    }

    @Get('vip/lossback/summary')
    @Security('cognito')
    @Response<UnauthorizedError>(401)
    public async getVIPLossbackWageringSummary(): Promise<StatisticsVIPLossbackWageringSummary> {
        if (this.user.level < 1)
            throw new UnauthorizedError(`You must be a VIP to access this data.`);

        const dayMoment = moment().day('Sunday');
        const startOfWeek = dayMoment.clone().startOf('week').toDate();
        const endOfWeek = dayMoment.clone().endOf('week').toDate();

        return this.statsManager.getVIPLossbackWageringSummary(this.user.id, startOfWeek, endOfWeek);
    }
}
