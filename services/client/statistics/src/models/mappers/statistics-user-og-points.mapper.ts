import { Singleton } from '@tcom/platform/lib/core/ioc';
import { StatisticsUserOGPointsDetailModel } from '../statistics-user-og-points-detail.model';
import { StatisticsUserOGPointsModel } from '../statistics-user-og-points.model';
import { StatisticsUserOGPoints } from '@tcom/platform/lib/statistics';

@Singleton
export class StatisticsUserOGPointsModelMapper {
    public map(points: StatisticsUserOGPoints, additionalPointsMap?: { [key: string]: number }): StatisticsUserOGPointsModel {
        const details: { [key: string]: StatisticsUserOGPointsDetailModel } = {};
        Object.entries(points.detail).forEach(([key, detail]) => {
            const additionalPoints = additionalPointsMap?.[key] || 0;
            details[key] = {
                sweepPoints: detail.sweepPoints,
                crashPlinkoPoints: detail.crashPlinkoPoints,
                wageringPoints: detail.wageringPoints,
                livePoints: detail.livePoints,
                miniGamePoints: detail.miniGamePoints,
                totalPoints: detail.totalPoints + additionalPoints,
                pointsMultiplier: detail.pointsMultiplier,
                additionalPoints,
                dateFrom: detail.dateFrom,
                dateTo: detail.dateTo,
            };
        });

        if (additionalPointsMap)
            Object.entries(additionalPointsMap).forEach(([key, value]) => {
                if (details[key])
                    return;

                details[key] = {
                    sweepPoints: 0,
                    crashPlinkoPoints: 0,
                    wageringPoints: 0,
                    livePoints: 0,
                    totalPoints: value,
                    additionalPoints: value,
                    dateFrom: new Date(),
                    dateTo: new Date()
                };
            });

        return {
            userId: points.userId,
            detail: details,
            lastUpdated: points.lastUpdated
        };
    }
}