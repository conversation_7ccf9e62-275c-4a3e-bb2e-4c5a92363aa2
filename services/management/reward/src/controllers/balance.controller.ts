import { AdminController, Body, Get, Put, Query, Route, Security, Tags, Post, Path } from '@tcom/platform/lib/api';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { NotFoundError, PagedResult } from '@tcom/platform/lib/core';
import {
    NewRewardBalanceConfiguration,
    RewardBalance,
    RewardBalanceConfiguration,
    RewardBalanceConfigurationManager,
    RewardBalanceConfigurationUpdate,
    RewardBalanceFilter,
    RewardBalanceManager,
    RewardBalanceStatus,
    RewardBalanceType,
    RewardTemplateSourceType
} from '@tcom/platform/lib/reward';

@Tags('Reward Balance')
@Route('reward/balance')
@Security('admin')
@LogClass()
export class RewardBalanceController extends AdminController {
    constructor(
        @Inject private readonly manager: RewardBalanceManager,
        @Inject private readonly configurationManager: RewardBalanceConfigurationManager) {
        super();
    }

    /**
     * @summary Gets rewards balance
     */
    @Get()
    @Security('admin', ['reward:balance:read'])
    public async getAll(
        @Query() id?: number,
        @Query() userId?: number,
        @Query() dateFrom?: Date,
        @Query() dateTo?: Date,
        @Query() status?: RewardBalanceStatus[],
        @Query() type?: RewardBalanceType[],
        @Query() sourceType?: RewardTemplateSourceType[],
        @Query() page: number = 1,
        @Query() pageSize: number = 20,
        @Query() order: string = 'createTime',
    ): Promise<PagedResult<RewardBalance>> {
        const filter: RewardBalanceFilter = {
            id,
            userId,
            dateFrom,
            dateTo,
            status,
            type,
            sourceType,
            page,
            pageSize,
            order: {
                [`${order}`]: 'DESC'
            }
        };

        return this.manager.getAll(filter);
    }

    @Get('configuration/all')
    @Security('admin', ['reward:balance:read'])
    public async getAllConfigurations(): Promise<RewardBalanceConfiguration[]> {
        return this.configurationManager.getAll();
    }

    @Get('configuration')
    @Security('admin', ['reward:balance:read'])
    public async getConfiguration(@Query() type: RewardBalanceType): Promise<RewardBalanceConfiguration> {
        const configuration = await this.configurationManager.getByType(type);

        if (!configuration)
            throw new NotFoundError(`Reward balance configuration with type ${type} not found`);

        return configuration;
    }

    @Post('configuration')
    @Security('admin', ['reward:balance:write'])
    public async addConfiguration(@Body() configuration: NewRewardBalanceConfiguration): Promise<void> {
        await this.configurationManager.add(configuration);
    }

    @Put('configuration/{id}')
    @Security('admin', ['reward:balance:write'])
    public async updateConfiguration(@Path() id: number, @Body() configuration: RewardBalanceConfigurationUpdate): Promise<void> {
        await this.configurationManager.update(id, configuration);
    }
}