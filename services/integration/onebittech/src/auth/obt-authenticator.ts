import { Inject, <PERSON>ton } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { ApiRequest } from '@tcom/platform/lib/api';
import { UnauthorizedError } from '@tcom/platform/lib/core';
import { OneBitTechAuthRequestModel, OneBitTechResponseModel } from '@tcom/platform/lib/integration/onebittech/models';
import { SportbookSessionManager } from '@tcom/platform/lib/sport';
import { OneBitTechErrorMapper } from '../utilities/obt-error.mapper';
import { ok } from '../utilities/obt-response-result';

@Singleton
@LogClass()
export class OneBitTechAuthenticator {
    constructor(
        @Inject private readonly sessionManager: SportbookSessionManager,
        @Inject private readonly errorMapper: OneBitTechErrorMapper) {
    }

    public async execute(request: ApiRequest): Promise<OneBitTechResponseModel> {
        try {
            console.log('AUTH REQUEST', request.body);
            const body = request.body as OneBitTechAuthRequestModel;
            const existingSession = await this.sessionManager.getByTokenId(body.sessionToken);
            console.log('AUTH EXISTING SESSION', existingSession);

            if (!existingSession)
                throw new UnauthorizedError('Session not found');

            return ok();
        } catch (error) {
            console.log('AUTH ERROR', error);
            return this.errorMapper.map(error);
        }
    }
}