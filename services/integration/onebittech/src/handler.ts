import initializeApi, { ErrorHandlerOptions } from '@tcom/platform/lib/api/api-initializer';
// Import All Controllers: Required for swagger and route generation
import './controllers/obt.controller';
import { RegisterRoutes } from './routes';
import { OneBitTechAuthenticator } from './auth';

ErrorHandlerOptions.reportAll = true;
export const app = initializeApi(RegisterRoutes, {
    geoResolution: false,
    authenticators: {
        onebittech: OneBitTechAuthenticator
    },
    restrictOrigin: false,
    ipBlacklistCheck: false
});