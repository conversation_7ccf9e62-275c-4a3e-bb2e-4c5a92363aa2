import { Body, Controller, Post, Route, Security, SuccessResponse, Tags } from '@tcom/platform/lib/api';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { LogClass, LogLevel } from '@tcom/platform/lib/core/logging';
import { UnauthorizedError, InsufficientFundsError, BadRequestError } from '@tcom/platform/lib/core';
import { OneBitTechAuthRequestModel, OneBitTechRequestModel, OneBitTechResponseModel } from '@tcom/platform/lib/integration/onebittech/models';
import { OneBitTechRequestProcessor } from '../utilities/obt-request-processor';
import { OneBitTechErrorMapper } from '../utilities/obt-error.mapper';

@Tags('OneBitTech')
@Route('onebittech')
@Security('onebittech')
@LogClass({ level: LogLevel.Info })
export class OneBitTechController extends Controller {
    constructor(
        @Inject private readonly processor: OneBitTechRequestProcessor,
        @Inject private readonly errorMapper: OneBitTechErrorMapper) {
        super();
    }

    /**
     * @summary Authenticates user
     */
    @Post('auth')
    @SuccessResponse(200, 'Ok')
    public async auth(@Body() request: OneBitTechAuthRequestModel): Promise<OneBitTechResponseModel> {
        try {
            const response = await this.processor.authentication(request, this._request);

            // Check if the response contains an error and optionally throw HTTP errors
            if (response.error !== 0) {
                console.log('AUTH FAILED', {
                    error: response.error,
                    message: response.message,
                    request: request
                });

                // Optionally throw HTTP errors based on error codes
                this.handleResponseError(response);
            }

            return response;
        } catch (error) {
            console.log('AUTH ERROR', error);
            return this.errorMapper.map(error);
        }
    }

    private handleResponseError(response: OneBitTechResponseModel): void {
        switch (response.error) {
            case 401:
                throw new UnauthorizedError(response.message || 'Authentication failed');
            case 202:
                throw new InsufficientFundsError(response.message || 'Insufficient funds');
            case 203:
                throw new BadRequestError(response.message || 'Invalid request hash');
            default:
                // For other errors, you can choose to throw a generic error or just log
                // throw new BadRequestError(response.message || 'Request failed');
                break;
        }
    }

    /**
     * @summary OneBitTech request
     */
    @Post('change-balance')
    @SuccessResponse(200, 'Ok')
    public async changeBalance(@Body() request: OneBitTechRequestModel): Promise<OneBitTechResponseModel> {
        return this.processor.process(request, this._request);
    }
}