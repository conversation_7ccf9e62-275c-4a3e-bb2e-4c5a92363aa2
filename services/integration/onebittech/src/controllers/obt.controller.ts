import { Body, Controller, Post, Route, Security, SuccessResponse, Tags } from '@tcom/platform/lib/api';
import { Inject } from '@tcom/platform/lib/core/ioc';
import { LogClass, LogLevel } from '@tcom/platform/lib/core/logging';
import { OneBitTechAuthRequestModel, OneBitTechRequestModel, OneBitTechResponseModel } from '@tcom/platform/lib/integration/onebittech/models';
import { OneBitTechRequestProcessor } from '../utilities/obt-request-processor';
import { OneBitTechErrorMapper } from '../utilities/obt-error.mapper';

@Tags('OneBitTech')
@Route('onebittech')
@Security('onebittech')
@LogClass({ level: LogLevel.Info })
export class OneBitTechController extends Controller {
    constructor(
        @Inject private readonly processor: OneBitTechRequestProcessor,
        @Inject private readonly errorMapper: OneBitTechErrorMapper) {
        super();
    }

    /**
     * @summary Authenticates user
     */
    @Post('auth')
    @SuccessResponse(200, 'Ok')
    public async auth(@Body() request: OneBitTechAuthRequestModel): Promise<OneBitTechResponseModel> {
        try {
            return await this.processor.authentication(request, this._request);
        } catch (error) {
            console.log('AUTH ERROR', error);
            return this.errorMapper.map(error);
        }
    }

    /**
     * @summary OneBitTech request
     */
    @Post('change-balance')
    @SuccessResponse(200, 'Ok')
    public async changeBalance(@Body() request: OneBitTechRequestModel): Promise<OneBitTechResponseModel> {
        return this.processor.process(request, this._request);
    }
}