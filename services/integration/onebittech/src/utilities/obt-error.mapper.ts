import { <PERSON><PERSON> } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { InsufficientFundsError, UnauthorizedError } from '@tcom/platform/lib/core';
import { OneBitTechResponse } from '@tcom/platform/lib/integration/onebittech/interfaces/response';

@Singleton
@LogClass()
export class OneBitTechErrorMapper {
    public map(error: Error): OneBitTechResponse {
        if (error instanceof UnauthorizedError)
            return this.buildError(401, error.message);

        if (error instanceof InsufficientFundsError)
            return this.buildError(202, error.message);

        return this.buildError(500, error.message);
    }

    private buildError(error: number, message: string): OneBitTechResponse {
        return {
            error,
            message
        };
    }
}

// Invalid request hash 203