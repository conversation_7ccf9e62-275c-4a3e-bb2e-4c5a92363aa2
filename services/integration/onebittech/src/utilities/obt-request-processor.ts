import { Inject, Singleton } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { BadRequestError } from '@tcom/platform/lib/core';
import { ApiRequest, IdempotencyCache } from '@tcom/platform/lib/api';
import { SportbookProcessorFactory } from '@tcom/platform/lib/sport/processor';
import { OneBitTechAuthRequestModel, OneBitTechRequestModel, OneBitTechResponseModel } from '@tcom/platform/lib/integration/onebittech/models';
import { OneBitTechBetCancelRequest, OneBitTechBetCloseRequest, OneBitTechBetLoseRequest, OneBitTechBetslipRequest, OneBitTechBetWinRequest, OneBitTechLoserRollbackRequest, OneBitTechOperation, OneBitTechValidationRequest, OneBitTechWinRollbackRequest } from '@tcom/platform/lib/integration/onebittech/interfaces/request';
import { OneBitTechAuthVerifier } from '../auth';
import { OneBitTechErrorMapper, OneBitTechRequestMapper, OneBitTechResponseMapper } from '../utilities';

@Singleton
@LogClass()
export class OneBitTechRequestProcessor {
    constructor(
        @Inject private readonly idempotencyCache: IdempotencyCache,
        @Inject private readonly authVerifier: OneBitTechAuthVerifier,
        @Inject private readonly requestMapper: OneBitTechRequestMapper,
        @Inject private readonly responseMapper: OneBitTechResponseMapper,
        @Inject private readonly errorMapper: OneBitTechErrorMapper,
        @Inject private readonly sportbookProcessorFactory: SportbookProcessorFactory) {
    }

    private readonly operationHandlers: Partial<Record<OneBitTechOperation, (request: OneBitTechRequestModel, apiRequest: ApiRequest) => Promise<OneBitTechResponseModel>>> = {
        [OneBitTechOperation.Validation]: (request, apiRequest) => this.validation(request as OneBitTechValidationRequest, apiRequest),
        [OneBitTechOperation.ValidationFail]: (request, apiRequest) => this.validationFail(request as OneBitTechValidationRequest, apiRequest),
        [OneBitTechOperation.Betslip]: (request, apiRequest) => this.betslip(request as OneBitTechBetslipRequest, apiRequest),
        [OneBitTechOperation.BetWin]: (request, apiRequest) => this.betWin(request as OneBitTechBetWinRequest, apiRequest),
        [OneBitTechOperation.BetLose]: (request, apiRequest) => this.betLose(request as OneBitTechBetLoseRequest, apiRequest),
        [OneBitTechOperation.BetCancel]: (request, apiRequest) => this.betCancel(request as OneBitTechBetCancelRequest, apiRequest),
        [OneBitTechOperation.BetClose]: (request, apiRequest) => this.betClose(request as OneBitTechBetCloseRequest, apiRequest),
        [OneBitTechOperation.WinRollback]: (request, apiRequest) => this.winRollback(request as OneBitTechWinRollbackRequest, apiRequest),
        [OneBitTechOperation.LoserRollback]: (request, apiRequest) => this.loserRollback(request as OneBitTechLoserRollbackRequest, apiRequest),
    };

    public async process(request: OneBitTechRequestModel, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        const handler = this.operationHandlers[request.op];

        if (!handler) {
            throw new BadRequestError(`Unsupported Sportbook Operation: ${request.op}`);
        }

        return handler(request, apiRequest);
    }

    public async authentication(request: OneBitTechAuthRequestModel, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        try {
            return this.authVerifier.verify(request, apiRequest);
        } catch (err) {
            return this.errorMapper.map(err);
        }
    }

    private async processRequest(request: OneBitTechRequestModel, apiRequest: ApiRequest, cacheKeyPrefix: string): Promise<OneBitTechResponseModel> {
        try {
            // Get transaction ID - validation requests use validationTxId, others use txId
            const transactionId = 'validationTxId' in request ? request.validationTxId : request.txId;
            const cacheKey = `${cacheKeyPrefix}-${transactionId}`;

            return await this.idempotencyCache.get(cacheKey, async () => {
                await this.authVerifier.verify(request, apiRequest);
                const action = await this.requestMapper.map(request);
                const processor = this.sportbookProcessorFactory.create(action.type);
                const outcome = await processor.process(action);
                return this.responseMapper.map(outcome);
            });
        } catch (error) {
            return this.errorMapper.map(error);
        }
    }

    private async validation(request: OneBitTechValidationRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'validation');
    }

    private async validationFail(request: OneBitTechValidationRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'validation-fail');
    }

    private async betslip(request: OneBitTechBetslipRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'betslip');
    }

    private async betWin(request: OneBitTechBetWinRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-win');
    }

    private async betLose(request: OneBitTechBetLoseRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-lose');
    }

    private async betCancel(request: OneBitTechBetCancelRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-cancel');
    }

    private async betClose(request: OneBitTechBetCloseRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-close');
    }

    // Awaiting OBT to confirm. Possibly it only needs to update the betslip status and wait for a win or lose request
    private async winRollback(request: OneBitTechWinRollbackRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'win-rollback');
    }

    // Awaiting OBT to confirm. Possibly it only needs to update the betslip status and wait for a win or lose request
    private async loserRollback(request: OneBitTechLoserRollbackRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'loser-rollback');
    }
}