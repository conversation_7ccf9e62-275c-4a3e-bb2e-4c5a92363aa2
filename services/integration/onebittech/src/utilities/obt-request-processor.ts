import { Inject, Singleton } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { BadRequestError } from '@tcom/platform/lib/core';
import { ApiRequest, IdempotencyCache } from '@tcom/platform/lib/api';
import { SportbookProcessorFactory } from '@tcom/platform/lib/sport/processor';
import { OneBitTechAuthRequestModel, OneBitTechRequestModel, OneBitTechResponseModel } from '@tcom/platform/lib/integration/onebittech/models';
import { OneBitTechBetCancelRequest, OneBitTechBetCloseRequest, OneBitTechBetLoseRequest, OneBitTechBetslipRequest, OneBitTechBetWinRequest, OneBitTechLoserRollbackRequest, OneBitTechOperation, OneBitTechValidationRequest, OneBitTechWinRollbackRequest } from '@tcom/platform/lib/integration/onebittech/interfaces/request';
import { OneBitTechAuthVerifier } from '../auth';
import { OneBitTechErrorMapper, OneBitTechRequestMapper, OneBitTechResponseMapper } from '../utilities';

@Singleton
@LogClass()
export class OneBitTechRequestProcessor {
    constructor(
        @Inject private readonly idempotencyCache: IdempotencyCache,
        @Inject private readonly authVerifier: OneBitTechAuthVerifier,
        @Inject private readonly requestMapper: OneBitTechRequestMapper,
        @Inject private readonly responseMapper: OneBitTechResponseMapper,
        @Inject private readonly errorMapper: OneBitTechErrorMapper,
        @Inject private readonly sportbookProcessorFactory: SportbookProcessorFactory) {
    }

    public async process(request: OneBitTechRequestModel, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        switch (request.op) {
            case OneBitTechOperation.Validation: {
                const validationRequest = request as OneBitTechValidationRequest;
                return this.validation(validationRequest, apiRequest);
            }

            case OneBitTechOperation.ValidationFail: {
                const validationFailRequest = request as OneBitTechValidationRequest;
                return this.validationFail(validationFailRequest, apiRequest);
            }

            case OneBitTechOperation.Betslip: {
                const betslipRequest = request as OneBitTechBetslipRequest;
                return this.betslip(betslipRequest, apiRequest);
            }

            case OneBitTechOperation.BetWin: {
                const betWinRequest = request as OneBitTechBetWinRequest;
                return this.betWin(betWinRequest, apiRequest);
            }

            case OneBitTechOperation.BetLose: {
                const betLoseRequest = request as OneBitTechBetLoseRequest;
                return this.betLose(betLoseRequest, apiRequest);
            }

            case OneBitTechOperation.BetCancel: {
                const betCancelRequest = request as OneBitTechBetCancelRequest;
                return this.betCancel(betCancelRequest, apiRequest);
            }

            case OneBitTechOperation.BetClose: {
                const betCloseRequest = request as OneBitTechBetCloseRequest;
                return this.betClose(betCloseRequest, apiRequest);
            }

            case OneBitTechOperation.WinRollback: {
                const winRollbackRequest = request as OneBitTechWinRollbackRequest;
                return this.winRollback(winRollbackRequest, apiRequest);
            }

            case OneBitTechOperation.LoserRollback: {
                const loserRollbackRequest = request as OneBitTechLoserRollbackRequest;
                return this.loserRollback(loserRollbackRequest, apiRequest);
            }

            default:
                throw new BadRequestError(`Unsupported Sportbook Operation: ${request.op}`);
        }
    }

    public async authentication(request: OneBitTechAuthRequestModel, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        try {
            return this.authVerifier.verify(request, apiRequest);
        } catch (err) {
            return this.errorMapper.map(err);
        }
    }

    private async processRequest(request: OneBitTechRequestModel, apiRequest: ApiRequest, cacheKeyPrefix: string): Promise<OneBitTechResponseModel> {
        try {
            // Get transaction ID - validation requests use validationTxId, others use txId
            const transactionId = 'validationTxId' in request ? request.validationTxId : request.txId;
            const cacheKey = `${cacheKeyPrefix}-${transactionId}`;

            return await this.idempotencyCache.get(cacheKey, async () => {
                await this.authVerifier.verify(request, apiRequest);
                const action = await this.requestMapper.map(request);
                const processor = this.sportbookProcessorFactory.create(action.type);
                const outcome = await processor.process(action);
                return this.responseMapper.map(outcome);
            });
        } catch (error) {
            return this.errorMapper.map(error);
        }
    }

    private async validation(request: OneBitTechValidationRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'validation');
    }

    private async validationFail(request: OneBitTechValidationRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'validation-fail');
    }

    private async betslip(request: OneBitTechBetslipRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'betslip');
    }

    private async betWin(request: OneBitTechBetWinRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-win');
    }

    private async betLose(request: OneBitTechBetLoseRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-lose');
    }

    private async betCancel(request: OneBitTechBetCancelRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-cancel');
    }

    private async betClose(request: OneBitTechBetCloseRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'bet-close');
    }

    // Awaiting OBT to confirm. Possibly it only needs to update the betslip status and wait for a win or lose request
    private async winRollback(request: OneBitTechWinRollbackRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'win-rollback');
    }

    // Awaiting OBT to confirm. Possibly it only needs to update the betslip status and wait for a win or lose request
    private async loserRollback(request: OneBitTechLoserRollbackRequest, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        return this.processRequest(request, apiRequest, 'loser-rollback');
    }
}