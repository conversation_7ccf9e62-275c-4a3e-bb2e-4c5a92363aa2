import { Inject, Singleton } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { BadRequestError } from '@tcom/platform/lib/core';
import { ApiRequest, IdempotencyCache } from '@tcom/platform/lib/api';
import { SportbookProcessorFactory } from '@tcom/platform/lib/sport/processor';
import { OneBitTechAuthRequestModel, OneBitTechRequestModel, OneBitTechResponseModel } from '@tcom/platform/lib/integration/onebittech/models';
import { OneBitTechOperation } from '@tcom/platform/lib/integration/onebittech/interfaces/request';
import { OneBitTechAuthVerifier } from '../auth';
import { OneBitTechErrorMapper, OneBitTechRequestMapper, OneBitTechResponseMapper } from '../utilities';

@Singleton
@LogClass()
export class OneBitTechRequestProcessor {
    constructor(
        @Inject private readonly idempotencyCache: IdempotencyCache,
        @Inject private readonly authVerifier: OneBitTechAuthVerifier,
        @Inject private readonly requestMapper: OneBitTechRequestMapper,
        @Inject private readonly responseMapper: OneBitTechResponseMapper,
        @Inject private readonly errorMapper: OneBitTechErrorMapper,
        @Inject private readonly sportbookProcessorFactory: SportbookProcessorFactory) {
    }

    private readonly operationHandlers: Partial<Record<OneBitTechOperation, (request: OneBitTechRequestModel, apiRequest: ApiRequest) => Promise<OneBitTechResponseModel>>> = {
        // Validation operations to be removed
        // [OneBitTechOperation.Validation]: (request, apiRequest) => this.processRequest(request, apiRequest, 'validation'),
        // [OneBitTechOperation.ValidationFail]: (request, apiRequest) => this.processRequest(request, apiRequest, 'validation-fail'),
        [OneBitTechOperation.Betslip]: (request, apiRequest) => this.processRequest(request, apiRequest, 'betslip'),
        [OneBitTechOperation.BetWin]: (request, apiRequest) => this.processRequest(request, apiRequest, 'bet-win'),
        [OneBitTechOperation.BetLose]: (request, apiRequest) => this.processRequest(request, apiRequest, 'bet-lose'),
        [OneBitTechOperation.BetCancel]: (request, apiRequest) => this.processRequest(request, apiRequest, 'bet-cancel'),
        [OneBitTechOperation.BetClose]: (request, apiRequest) => this.processRequest(request, apiRequest, 'bet-close'),
        [OneBitTechOperation.WinRollback]: (request, apiRequest) => this.processRequest(request, apiRequest, 'win-rollback'),
        [OneBitTechOperation.LoserRollback]: (request, apiRequest) => this.processRequest(request, apiRequest, 'loser-rollback'),
    };

    public async process(request: OneBitTechRequestModel, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        const handler = this.operationHandlers[request.op];

        if (!handler)
            throw new BadRequestError(`Unsupported Sportbook Operation: ${request.op}`);

        return handler(request, apiRequest);
    }

    public async authentication(request: OneBitTechAuthRequestModel, apiRequest: ApiRequest): Promise<OneBitTechResponseModel> {
        try {
            return this.authVerifier.verify(request, apiRequest);
        } catch (err) {
            return this.errorMapper.map(err);
        }
    }

    private async processRequest(request: OneBitTechRequestModel, apiRequest: ApiRequest, cacheKeyPrefix: string): Promise<OneBitTechResponseModel> {
        try {
            // Validation operations removed - all remaining operations use txId
            // const transactionId = 'validationTxId' in request ? request.validationTxId : request.txId;
            const cacheKey = `${cacheKeyPrefix}-${(request as any).txId}`;

            return await this.idempotencyCache.get(cacheKey, async () => {
                await this.authVerifier.verify(request, apiRequest);
                const action = await this.requestMapper.map(request);
                const processor = this.sportbookProcessorFactory.create(action.type);
                const outcome = await processor.process(action);
                return this.responseMapper.map(outcome);
            });
        } catch (error) {
            return this.errorMapper.map(error);
        }
    }
}