import { <PERSON>ton, Inject, IocContainer } from '@tcom/platform/lib/core/ioc';
import { LogClass } from '@tcom/platform/lib/core/logging';
import { lambdaHandler } from '@tcom/platform/lib/core';
import { CurrencyRepository } from '@tcom/platform/lib/banking/repositories';
import { CurrencyCache } from '@tcom/platform/lib/banking/cache';

interface Payload {
    code?: string;
}

@Singleton
@LogClass()
class RefreshCurrencyCacheHandler {
    constructor(
        @Inject private readonly repository: CurrencyRepository,
        @Inject private readonly cache: CurrencyCache) {
    }

    public async execute(event: Payload): Promise<void> {
        if (event.code) {
            const currency = await this.repository.get(event.code);

            if (!currency)
                throw new Error(`Currency with code ${event.code} not found.`);

            await this.cache.store(currency);
        }

        const currencies = await this.repository.getAll();
        await this.cache.clear();
        await this.cache.store(...currencies);
    }
}

export const refreshCurrencyCache = lambdaHandler((event: Payload) => IocContainer.get(RefreshCurrencyCacheHandler).execute(event));