service: system-slack

plugins:
  - serverless-plugin-resource-tagging
  - serverless-plugin-aws-exponential-backoff
  - serverless-plugin-enabled
  - serverless-webpack
  - serverless-plugin-scripts

provider:
  versionFunctions: false
  name: aws
  runtime: nodejs20.x
  memorySize: ${self:custom.common.provider.memorySize}
  region: ${opt:region, "${self:custom.common.defaultRegion}"}
  stage: ${opt:stage}
  stackTags: ${self:custom.common.provider.stackTags}
  logRetentionInDays: ${self:custom.common.logRetention}
  iam: ${file(../serverless/iam.yml)}
  environment: ${file(../../serverless/environment.yml)}
  profile: ${self:custom.common.aws.profile}
  apiGateway:
    usagePlan: ${self:custom.common.provider.usagePlan}
  vpc: ${self:custom.common.provider.vpc}

custom:
  common: ${file(../../serverless/common.yml)}
  brand: ${file(../../brands.js):${self:custom.common.brandId}}
  webpack:
    includeModules: true
    packager: yarn
  scripts:
    hooks:
      "package:initialize": yarn build

functions:
  onWithdrawalRequestAdded:
    handler: src/handlers.onWithdrawalRequestAdded
    timeout: 30
    reservedConcurrency: 1
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Banking:WithdrawalRequest:Added
    environment:
      FUNCTION: onWithdrawalRequestAdded

  onWithdrawalRequestStatusChanged:
    handler: src/handlers.onWithdrawalRequestStatusChanged
    timeout: 30
    reservedConcurrency: 1
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Banking:WithdrawalRequest:StatusChanged
            Status:
              - Cancelled
    environment:
      FUNCTION: onWithdrawalRequestStatusChanged

  onJackpotPaidOut:
    handler: src/handlers.onJackpotPaidOut
    timeout: 30
    reservedConcurrency: 1
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Jackpot:PaidOut
    environment:
      FUNCTION: onJackpotPaidOut

  onSubscriptionStatusChanged:
    handler: src/handlers.onSubscriptionStatusChanged
    timeout: 30
    reservedConcurrency: 1
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Subscription:StatusChanged
    environment:
      FUNCTION: onSubscriptionStatusChanged

  onRewardTemplateLimitReached:
    handler: src/handlers.onRewardTemplateLimitReached
    timeout: 30
    reservedConcurrency: 1
    enabled: false
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${self:custom.common.aws.accountId}:platform-event
          displayName: "Platform Event Topic"
          filterPolicy:
            EventType:
              - Reward:Template:Limit:Reached
    environment:
      FUNCTION: onRewardTemplateLimitReached
