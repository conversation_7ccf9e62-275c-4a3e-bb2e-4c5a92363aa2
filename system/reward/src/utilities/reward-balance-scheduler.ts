import { Inject, Singleton } from '@tcom/platform/lib/core/ioc';
import Logger, { LogClass } from '@tcom/platform/lib/core/logging';
import { RewardBalance, RewardBalanceConfigurationManager, RewardBalanceStatus, RewardBalanceType, RewardBalanceManager } from '@tcom/platform/lib/reward';
import { RewardBalanceJobManager } from '@tcom/platform/lib/reward/balances/batch';
import { BalanceSchedulerChecker } from '@tcom/platform/lib/reward/balances/scheduler';
import moment from 'moment';

const BATCH_SIZE = 100;

@Singleton
@LogClass()
export class RewardBalanceScheduler {
    private readonly types = [
        RewardBalanceType.WinBack,
        RewardBalanceType.Daily,
        RewardBalanceType.DailyAllocation,
        RewardBalanceType.Weekly,
        RewardBalanceType.Monthly
    ];

    constructor(
        @Inject private readonly configurationManager: RewardBalanceConfigurationManager,
        @Inject private readonly schedulerChecker: BalanceScheduler<PERSON>he<PERSON>,
        @Inject private readonly manager: <PERSON><PERSON>BalanceManager,
        @Inject private readonly jobManager: RewardBalanceJobManager) {
    }

    public async run(): Promise<void> {
        const schedules = await this.getSchedule();

        if (!schedules.length) {
            Logger.info('No reward balance schedules to process');
            return;
        }

        await Promise.allSettled(schedules.map(type => this.processSchedule(type)));
    }

    public async getSchedule(): Promise<RewardBalanceType[]> {
        const now = moment().utc().toDate();
        const configurations = await this.configurationManager.getAllByType(this.types);

        if (!configurations.length) {
            Logger.info('No reward balance configurations found');
            return [];
        }

        const schedules: RewardBalanceType[] = [];

        for (const config of configurations) {
            if (!config.enabled)
                continue;

            const isReady = this.schedulerChecker.schedulerChecker(now, config);

            if (isReady)
                schedules.push(config.type);
        }

        return schedules;
    }

    private async processSchedule(type: RewardBalanceType): Promise<void> {
        Logger.info(`Processing reward balance schedule for ${type}...`);
        const now = type === RewardBalanceType.DailyAllocation
            ? undefined
            : moment().utc().startOf('minute').toDate();

        const rewards = await this.manager.getAllByTypeAndStatus<RewardBalance>(type, RewardBalanceStatus.InProgress, now, BATCH_SIZE);

        if (!rewards.length)
            return;

        await this.jobManager.create(type, rewards);
    }
}
