import { <PERSON>ton, Inject } from '@tcom/platform/lib/core/ioc';
import Logger, { LogClass, LogLevel } from '@tcom/platform/lib/core/logging';
import { Config, JsonSerialiser } from '@tcom/platform/lib/core';
import { RewardBalance, RewardBalanceManager, RewardBalanceType, RewardSourceEvent, RewardBalanceStatus, RewardBalanceConfigurationManager, RewardBalanceConfiguration, DailyAllocationRewardBalance } from '@tcom/platform/lib/reward';
import { UserBonusStatus, UserManager } from '@tcom/platform/lib/user';
import { RewardBalanceProcessor } from '../reward-balance-processor';
import { SchedulerTimers } from '@tcom/platform/lib/reward/balances/scheduler';
import AWS from 'aws-sdk';
import moment from 'moment';

@Singleton
@LogClass({ level: LogLevel.Info })
export class DefaultRewardBalanceProcessor implements RewardBalanceProcessor<RewardBalance>{
    constructor(
        @Inject private readonly serialiser: Json<PERSON>erial<PERSON>,
        @Inject private readonly manager: RewardBalanceManager,
        @Inject private readonly configurationManager: RewardBalanceConfigurationManager,
        @Inject private readonly schedulerTimers: SchedulerTimers,
        @Inject private readonly userManager: UserManager) {
    }

    public async process(payload: RewardBalance): Promise<void> {
        const config = await this.configurationManager.getByType(payload.type);

        if (!config || !config.enabled)
            return;

        if (payload.rewardAmount.isLessThanOrEqualTo(0)) {
            await this.manager.setClosed(payload.id, payload.userId, payload.type);
            return;
        }

        const user = await this.userManager.get(payload.userId);

        if (!user) {
            Logger.warn(`User ${payload.userId} not found.`);
            await this.manager.setClosed(payload.id, payload.userId, payload.type);
            return;
        }

        if (user.bonusStatus !== UserBonusStatus.Active) {
            Logger.info(`Reward ${payload.type} not awarded as user ${payload.userId} has rewards disabled.`);
            await this.processInactiveBonusStatus(payload);
            return;
        }

        const balance = await this.manager.get(payload.id);
        if (!balance || balance.status !== RewardBalanceStatus.InProgress)
            return;

        if (balance.type === RewardBalanceType.DailyAllocation) {
            const isValid = await this.checkDailyAllocation(balance);
            if (!isValid) {
                Logger.warn(`Daily allocation for user ${payload.userId} is not valid.`);
                await this.manager.setStatus(RewardBalanceStatus.Complete, payload.id);
                return;
            }
        }

        if (payload.type !== RewardBalanceType.DailyAllocation)
            await this.manager.remove(payload.userId, payload.type);

        const event: RewardSourceEvent = await this.getEvent(payload, config);
        await this.manager.setStatus(RewardBalanceStatus.Processing, payload.id);
        await this.sendMessage(event);
    }

    private async getEvent(entry: RewardBalance, config: RewardBalanceConfiguration): Promise<RewardSourceEvent> {
        const event: RewardSourceEvent = {
            source: entry.sourceType,
            userId: entry.userId,
            count: 1,
            amount: entry.rewardAmount,
            currencyCode: entry.currencyCode,
            data: {
                balanceId: entry.id,
                claimable: true,
                claimFromPeriodValue: moment().utc().startOf('minute').toDate()
            }
        };

        if (entry.type === RewardBalanceType.DailyAllocation) {
            const nextRewardDate = await this.getNextDailyAllocationDate(config);

            event.data = {
                ...event.data,
                claimable: false,
                claimFromPeriodValue: nextRewardDate,
            };
        }

        return event;
    }

    private async getNextDailyAllocationDate(config: RewardBalanceConfiguration): Promise<Date> {
        const now = moment().utc().startOf('minute').toDate();
        return this.schedulerTimers.scheduleTime(now, config);
    }

    private async checkDailyAllocation(entry: DailyAllocationRewardBalance): Promise<boolean> {
        const cachedBalance = await this.manager.getByUserId<DailyAllocationRewardBalance>(entry.userId, RewardBalanceType.DailyAllocation);

        if (!cachedBalance) {
            Logger.warn(`Cached Daily allocation for user ${entry.userId} not found.`);
            return false;
        }

        if (cachedBalance.id !== entry.id) {
            Logger.warn(`Cached Daily allocation for user ${entry.userId} does not match entry ${entry.id}.`);
            return false;
        }

        if (cachedBalance.count <= 0) {
            Logger.warn(`Cached Daily allocation for user ${entry.userId} has no remaining count.`);
            return false;
        }

        return true;
    }

    private async processInactiveBonusStatus(entry: RewardBalance): Promise<void> {
        if (entry.type === RewardBalanceType.DailyAllocation) {
            const allocationCount = await this.manager.updateDailyCount(entry.id);

            if (allocationCount && allocationCount.count === 0) {
                await this.manager.setStatus(RewardBalanceStatus.Complete, entry.id);
                await this.manager.remove(entry.userId, entry.type);
            }

            return;
        }

        await this.manager.setClosed(entry.id, entry.userId, entry.type);
    }

    private async sendMessage(event: RewardSourceEvent): Promise<void> {
        const sqs = new AWS.SQS({
            region: Config.region
        });

        await sqs.sendMessage({
            MessageBody: this.serialiser.serialise(event),
            QueueUrl: `https://sqs.${Config.region}.amazonaws.com/${Config.accountId}/reward-source`
        }).promise();
    }
}