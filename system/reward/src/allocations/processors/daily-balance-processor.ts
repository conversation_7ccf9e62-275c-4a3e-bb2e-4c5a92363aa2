import { Singleton, Inject } from '@tcom/platform/lib/core/ioc';
import Logger, { LogClass, LogLevel } from '@tcom/platform/lib/core/logging';
import { BigNumber, Config, JsonSerialiser } from '@tcom/platform/lib/core';
import { SendMessageBatchRequestEntry } from 'aws-sdk/clients/sqs';
import { DailyAllocationBalanceConfiguration, DailyBalanceConfiguration } from '@tcom/platform/lib/reward/balances/configuration';
import { RewardBalanceProcessor } from '../reward-balance-processor';
import { DailyRewardBalance, NewDailyAllocationRewardBalance, RewardBalanceManager, RewardBalanceStatus, RewardBalanceType, RewardSourceEvent, RewardTemplateSourceType, RewardBalanceConfigurationManager } from '@tcom/platform/lib/reward';
import { SchedulerTimers } from '@tcom/platform/lib/reward/balances/scheduler/scheduler-timers';
import { UserBonusStatus, UserManager } from '@tcom/platform/lib/user';
import { v4 as uuid } from 'uuid';
import moment from 'moment';
import AWS from 'aws-sdk';
import _ from 'lodash';

@Singleton
@LogClass({ level: LogLevel.Info })
export class DailyRewardBalanceProcessor implements RewardBalanceProcessor<DailyRewardBalance> {
    constructor(
        @Inject private readonly serialiser: JsonSerialiser,
        @Inject private readonly manager: RewardBalanceManager,
        @Inject private readonly configurationManager: RewardBalanceConfigurationManager,
        @Inject private readonly schedulerTimers: SchedulerTimers,
        @Inject private readonly userManager: UserManager) {
    }

    public async process(payload: DailyRewardBalance): Promise<void> {
        const config = await this.configurationManager.getAllByType([RewardBalanceType.Daily, RewardBalanceType.DailyAllocation]);

        const dailyConfig = config.find(c => c.type === RewardBalanceType.Daily) as DailyBalanceConfiguration;
        const dailyAllocationConfig = config.find(c => c.type === RewardBalanceType.DailyAllocation) as DailyAllocationBalanceConfiguration;

        if (!dailyConfig || !dailyAllocationConfig)
            throw new Error('Daily configurations are required');

        if (!config.length)
            return;

        const user = await this.userManager.get(payload.userId);

        if (!user) {
            Logger.warn(`User ${payload.userId} not found.`);
            await this.manager.setClosed(payload.id, payload.userId, payload.type);
            return;
        }

        if (user.bonusStatus !== UserBonusStatus.Active) {
            Logger.info(`Reward ${payload.type} not awarded as user ${payload.userId} has rewards disabled.`);
            await this.ongoingAllocations(payload);
            await this.manager.setClosed(payload.id, payload.userId, payload.type);
            return;
        }

        const allocation = await this.dailyAllocationPerWeek(payload, payload.rewardAmount, dailyConfig, dailyAllocationConfig);

        if (allocation.rewardAmount.isLessThanOrEqualTo(0)) {
            await this.manager.setClosed(payload.id, payload.userId, payload.type);
            return;
        }

        const balances = await this.manager.upsert(payload.userId, allocation);
        await this.manager.remove(payload.userId, RewardBalanceType.Daily);
        await this.initDailyReward(allocation, dailyAllocationConfig, ...balances.map(b => b.id));
        await this.manager.setStatus(RewardBalanceStatus.Complete, payload.id);
    }

    private async dailyAllocationPerWeek(entry: DailyRewardBalance, adjustedAmount: BigNumber, dailyConfig: DailyBalanceConfiguration, allocationConfig: DailyAllocationBalanceConfiguration): Promise<NewDailyAllocationRewardBalance> {
        if (!allocationConfig.interval)
            throw new Error('Daily Allocation configuration interval is required');

        if (!allocationConfig)
            throw new Error('Daily allocation is required');

        await this.ongoingAllocations(entry);

        const now = moment().utc().startOf('minute').toDate();
        const dateTo = this.schedulerTimers.scheduleTime(now, dailyConfig);

        const allocation: NewDailyAllocationRewardBalance = {
            type: RewardBalanceType.DailyAllocation,
            sourceType: RewardTemplateSourceType.DailyReward,
            dateFrom: moment().utc().toDate(),
            dateTo,
            userId: entry.userId,
            status: RewardBalanceStatus.InProgress,
            eligible: true,
            theoGGR: entry.theoGGR,
            parentId: entry.id,
            rewardAmount: new BigNumber(adjustedAmount).dividedBy(allocationConfig.totalAllocation).decimalPlaces(2),
            count: allocationConfig.totalAllocation,
            currencyCode: entry.currencyCode
        };

        return allocation;
    }

    private async initDailyReward(dailyAllocation: NewDailyAllocationRewardBalance, config: DailyAllocationBalanceConfiguration, ...balanceIds: number[]): Promise<void> {
        const sqs = new AWS.SQS({
            region: Config.region
        });

        for (const id of balanceIds) {
            let messages: SendMessageBatchRequestEntry[] = [];

            const event: RewardSourceEvent = {
                userId: dailyAllocation.userId,
                source: RewardTemplateSourceType.DailyReward,
                count: 1,
                amount: dailyAllocation.rewardAmount,
                currencyCode: dailyAllocation.currencyCode,
            };

            messages = this.initAllocations(id, dailyAllocation.userId, event, config);
            const chunks = _.chunk(messages, 5);

            for (const chunk of chunks)
                await sqs.sendMessageBatch({
                    Entries: chunk,
                    QueueUrl: `https://sqs.${Config.region}.amazonaws.com/${Config.accountId}/reward-source`
                }).promise();
        }
    }

    private initAllocations(balanceId: number, userId: number, event: RewardSourceEvent, config: DailyAllocationBalanceConfiguration): SendMessageBatchRequestEntry[] {
        const now = moment().utc().startOf('minute').toDate();
        const { initialAllocation, initialClaimable } = config;
        const nonClaimableAllocations = initialAllocation - initialClaimable;
        const claimFromPeriodValue = this.schedulerTimers.scheduleTime(now, config);

        const noClaim = _.fill(Array(nonClaimableAllocations), {
            Id: userId.toString(),
            MessageBody: this.serialiser.serialise({
                ...event,
                data: {
                    claimable: false,
                    claimFromPeriodValue,
                    balanceId
                }
            })
        });

        const claim = _.fill(Array(initialClaimable), null).map(() => ({
            Id: `${uuid()}-${userId.toString()}-claimable`,
            MessageBody: this.serialiser.serialise({
                ...event,
                data: {
                    claimable: true,
                    claimFromPeriodValue: moment().utc().startOf('minute').toDate(),
                    balanceId
                },
            }),
        }));

        return _.concat(noClaim, claim);
    }

    private async ongoingAllocations(entry: DailyRewardBalance): Promise<void> {
        const openAllocations = await this.manager.getOpenDailyAllocation(entry.userId);

        if (openAllocations.length) {
            Logger.info(`Closing ${openAllocations.length} open daily allocations for user ${entry.userId}...`);
            for (const alloc of openAllocations)
                await this.manager.setStatus(RewardBalanceStatus.Complete, alloc.id);
        }
    }
}
