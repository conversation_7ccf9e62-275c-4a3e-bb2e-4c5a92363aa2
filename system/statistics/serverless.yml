service: system-statistics

plugins:
  - serverless-plugin-resource-tagging
  - serverless-plugin-aws-exponential-backoff
  - serverless-plugin-enabled
  - serverless-webpack
  - serverless-plugin-scripts

provider:
  versionFunctions: false
  name: aws
  runtime: nodejs20.x
  memorySize: ${self:custom.common.provider.memorySize}
  region: ${opt:region, "${self:custom.common.defaultRegion}"}
  stage: ${opt:stage}
  stackTags: ${self:custom.common.provider.stackTags}
  logRetentionInDays: ${self:custom.common.logRetention}
  iam: ${file(../serverless/iam.yml)}
  environment: ${file(../../serverless/environment.yml)}
  profile: ${self:custom.common.aws.profile}
  apiGateway:
    usagePlan: ${self:custom.common.provider.usagePlan}
  vpc: ${self:custom.common.provider.vpc}

custom:
  common: ${file(../../serverless/common.yml)}
  brand: ${file(../../brands.js):${self:custom.common.brandId}}
  webpack:
    includeModules: true
    packager: yarn
  scripts:
    hooks:
      "package:initialize": yarn build

functions:
  generateTotals:
    timeout: 900
    handler: src/handlers.generateTotals
    events:
      - schedule:
          rate: cron(*/30 * * * ? *)
          enabled: true
          input: '{"useWarehouse": true}'
    environment:
      FUNCTION: generateTotals

  generateTopWinsAll:
    timeout: 900
    handler: src/handlers.generateTopWins
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          enabled: true
          input: '{"useWarehouse": true}'
    environment:
      FUNCTION: generateTopWins

  generateTopWins1:
    timeout: 900
    handler: src/handlers.generateTopWins
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          enabled: true
          input: '{"days": 1, "minimumWinnings": 100, "useWarehouse": true}'
    environment:
      FUNCTION: generateTopWins

  generateTopWins7:
    timeout: 900
    handler: src/handlers.generateTopWins
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          enabled: true
          input: '{"days": 7, "useWarehouse": true}'
    environment:
      FUNCTION: generateTopWins

  generateTopWins30:
    timeout: 900
    handler: src/handlers.generateTopWins
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          enabled: true
          input: '{"days": 30, "useWarehouse": true}'
    environment:
      FUNCTION: generateTopWins

  userOGPoints:
    timeout: 900
    handler: src/handlers.userOGPoints
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          input: '{"identifier": "new"}'
    environment:
      FUNCTION: userOGPoints

  userOGPointsRaf:
    timeout: 900
    handler: src/handlers.userOGPoints
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          input: '{"identifier": "raf"}'
    environment:
      FUNCTION: userOGPoints

  userOGPointsSocial:
    timeout: 900
    handler: src/handlers.userOGPoints
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          input: '{"identifier": "social"}'
    environment:
      FUNCTION: userOGPoints

  userOGPointsNFTMultiplier:
    timeout: 900
    enabled: false
    handler: src/handlers.userOGPoints
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          input: '{"identifier": "season3"}'
    environment:
      FUNCTION: userOGPoints

  userOGPointsCasino:
    timeout: 900
    handler: src/handlers.userOGPoints
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          input: '{"identifier": "casino" }'
    environment:
      FUNCTION: userOGPoints

  generateUserTopWinsAll:
    timeout: 900
    handler: src/handlers.generateUserTopWins
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          enabled: true
    environment:
      FUNCTION: generateUserTopWins

  userTotalSpins:
    timeout: 900
    handler: src/handlers.userTotalSpins
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          enabled: true
          input: '{"useWarehouse": true}'
    environment:
      FUNCTION: userTotalSpins

  generateLiabilities:
    timeout: 900
    handler: src/handlers.generateLiabilities
    events:
      - schedule:
          rate: cron(*/15 * * * ? *)
          enabled: true
    environment:
      FUNCTION: generateLiabilities

  userOGPointsAdjustment:
    handler: src/handlers.userOGPointsAdjustment
    reservedConcurrency: 1
    timeout: 300
    environment:
      FUNCTION: userOGPointsAdjustment
